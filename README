# EasyCodeRuleDataServer

## 快速开始

1. clone 代码

```shell
git clone ssh://*******************:8235/baidu/netdisk/EasyCodeRuleDataServer
``` 

2. 安装 tangram

```shell
go install -trimpath -gcflags "all=-N -l" icode.baidu.com/baidu/netdisk/tangram/cmd/tangram@latest
```

3. 拉取 Makefile

```shell
git clone ssh://*******************:8235/baidu/netdisk/tangram-make && mv tangram-make/Makefile . && rm -rf tangram-make
```

4. 进入目录构建并启动进程

```shell
cd EasyCodeRuleDataServer && make run
```

## 更多详细说明：

[Tangram 文档](http://tangram.baidu-int.com/)