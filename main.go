/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: APP入口函数
 */
package main

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_module"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
	router "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/routers"
)

var Module = tangram_module.NewModule(start, stop)

func start() error {
	conf.Init()
	err := easy.Start(router.SetRouter(), "method")
	if err != nil {
		return err
	}
	if err := easy.ScheduledStart(router.SetSchedulerRouter()); err != nil {
		return err
	}
	return nil
}

func stop() {
	// do something after server close listen and before exit,not ensure it have enough time to exec finish.
}
