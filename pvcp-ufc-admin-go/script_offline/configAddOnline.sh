#!/bin/bash
service="pcs-default"
bns="TODO"
type="notin"
product="pcs业务"
owner="chezhuo"
isfakebns="1"   # 0 表示是真的bns，1表示是fake bns
ips="[\"**************:80\"]"
route_rule="{}"
#"{\"/aaa/bbb\":{\"app1\":{\"header\":{\"header_1\":\"value_1\",\"header_2\":\"value_2\"},\"query_string\":{\"a1\":\"b1\",\"a2\":\"b2\"}},\"app2\":{\"header\":{\"header_1\":\"value_3\",\"header_2\":\"value_4\"},\"query_string\":{\"a1\":\"b3\",\"a2\":\"b4\"}}}}"
route_app="{}"
#"{\"app1\":{\"pcs-online-ufc-admin.orp.yq\":50,\"group.wangpan-GlobalNetdiskTask.wangpan.all\":50},\"app2\":{\"group.wangpan-testNewAppCreate.wangpan.all\":100}}"

php serviceAddForGlobalSandbox.php $service $bns $type $product $owner $isfakebns $ips $route_rule $route_app