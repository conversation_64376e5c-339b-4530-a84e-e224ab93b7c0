<?php


$len = count($argv);
if ($len != 10) {
    echo "args is invalid, len is $len\n";
    exit;
}

$service = $argv[1];
$bns = $argv[2];
$type = $argv[3];
$product = $argv[4];
$person = $argv[5];
$is_fake_bns = $argv[6];
$fake_bns_ips = $argv[7];
$advanced_route_rule = $argv[8];
$advanced_route_app = $argv[9];

echo "service: $service\n";
echo "bns: $bns\n";
echo "type: $type\n";
echo "product: $product\n";
echo "person: $person\n";
echo "is_fake_bns: $is_fake_bns\n";
echo "fake_bns_ips: $fake_bns_ips\n";
echo "route_rule: $advanced_route_rule\n";
echo "route_app: $advanced_route_app\n";


$res = serviceAdd($service , $bns, $type, $product, $person, $is_fake_bns, $fake_bns_ips, $advanced_route_rule, $advanced_route_app);
$a = json_decode($res ,true);
print($a);
if ($a["error_code"] == 0 ){
    print("add" . $service . " config success \n");
}else{
    print("add" . $service . " config fail \n");
    $errmsg = $a["error_msg"];
    printf("error_msg is  $errmsg");
}


function serviceAdd($service , $bns, $type, $product, $person, $is_fake_bns, $fake_bns_ips, $advanced_route_rule, $advanced_route_app) {
    $breaker_policy = " ";
    $request_reset_interval = 0;
    if ($type == "bypass"){
        $breaker_policy = "{\"default\":{\"PA\":{\"policy\":1,\"regular\":\"if http_code < 500 then return 1 else return -1 end\"},\"PB\":{\"max_fail\":5,\"policy\":2},\"PC\":{\"forbid_timeout\":180,\"policy\":1},\"PD\":{\"policy\":1,\"ratio\":30},\"PE\":{\"forbid_timeout\":10,\"max_ratio\":80,\"min_ratio\":0,\"policy\":2,\"policy_temp\":5,\"ratio_interval\":20,\"update_interval\":10}}}";
        $request_reset_interval = 60;
    } else{
        $breaker_policy = "{\"default\":{\"PA\":{\"policy\":1,\"regular\":\"if http_code < 500 then return 1 else return -1 end\"},\"PB\":{\"max_fail\":5,\"policy\":1},\"PC\":{\"check_ok_times\":2,\"forbid_timeout\":120,\"policy\":2},\"PD\":{\"policy\":1,\"ratio\":30},\"PE\":{\"forbid_timeout\":10,\"max_ratio\":80,\"min_ratio\":0,\"policy\":2,\"policy_temp\":6,\"ratio\":60,\"ratio_interval\":20,\"update_interval\":10}}}";
        $request_reset_interval = 1800;
    }

    $fake_bns_exist = false;
    $fake_bns_list_data = "";
    //$smallflow_bns_name  = "";
    //$fake_bns_list_smallflow_data = "";
    if ($is_fake_bns == "1") {
        $fake_bns_exist = true;
        $bns = "fakebns-$service-0";
        //$smallflow_bns_name = "fakebns-sma-$serivce-0";
        $fake_bns_list_data = "{\"$bns\": $fake_bns_ips}";
    }
    
    $url = "http://************:80/rest/2.0/pvcp/ufcadmin/config?method=add";
    $param = array(
        "service_name" => $service,
        "product" => $product,
        "person" => $person,
        "mtime" => time(),
        "request_reset_interval" => $request_reset_interval,
        "bp" => 1,
        "bns_list" => "{\"" .$bns."\":100}",
        "send_timeout"=> 3000,
        "max_fail" => 1,
        "read_timeout" => 3000,
        "connect_timeout" => 1000,
        "black_list" => "",
        "small_flow_small_list" => "",
        "backends_max_tries"=> 2,
        "breaker_policy" => $breaker_policy,

        "advanced_route_rule" => $advanced_route_rule,
        "advanced_route_app" => $advanced_route_app,

        "fake_bns_exist" => $fake_bns_exist,
        "fake_bns_list_data" => $fake_bns_list_data, //
    );
    
    $res = RequestPost($url, $param);
    return $res;
}


    
function RequestGet($url, $timeout = 3, $headers = array()){
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $content = curl_exec($ch);
    curl_close($ch);

    return $content;
}

/**
 * @param string $url
 * @param string $param
 */
function RequestPost($url, $param = array()) {
    if (empty($url) || empty($param)) {
        return false;
    }

    //初始化curl
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HEADER, 0);

    $paramStr = json_encode($param);

    //要求结果为字符串且输出到屏幕上
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $paramStr);

    $headers = array(
        'Cache-Control: no-cache',
        'Content-Type: application/json',
        'Content-Length: ' . strlen($paramStr)
    );

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $content = curl_exec($ch);
    curl_close($ch);

    return $content;
}
