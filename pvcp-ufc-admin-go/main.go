package main

import (
	"fmt"
	"math/rand"
	"os"
	"os/signal"
	"runtime"
	"runtime/pprof"
	"syscall"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/framework"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/models"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/router"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

func main() {
	var err error

	models.InitMapConf()

	err = models.ParseBaseConf()
	if err != nil {
		fmt.Println("ufc-admin-go base conf error not nil, err:", err)
		golog.Error("ufc-admin-go base conf error not nil, err: %v", err)
		os.Exit(-1)
	}

	maxCpuNums := models.BaseConf.Server.MaxCpuNum
	cpuNum := runtime.NumCPU()
	if maxCpuNums <= 0 || maxCpuNums > cpuNum {
		maxCpuNums = cpuNum
	}
	runtime.GOMAXPROCS(maxCpuNums)
	golog.Info("[message:ufc-admin-go max cpu num is: %+v]", maxCpuNums)

	//init log config
	logFile := models.BaseConf.Log.LogFile
	logLevel := models.BaseConf.Log.LogLevel
	utils.Loginit(logFile, logLevel)
	golog.Info("[message:ufc-admin-go base config is: %+v]", models.BaseConf)
	golog.Info("[message:ufc-admin-go special rule config is: %+v]", models.BaseConf)
	golog.Info("[message:ufc-admin-go normal rule config is: %+v]", models.BaseConf)

	// 日志切分
	golog.EnableRotate(time.Hour)
	golog.SetbackupCount(24)
	setupSignal()

	if err := models.CheckConfigValid(); err != nil {
		golog.Error("table name is not valid, err is %s", err.Error())
		os.Exit(-1)
	}

	utils.UfcMetaInfoTableName = models.BaseConf.Mysql.UfcMetaInfoTable
	utils.CloudCacheUfc = models.BaseConf.Cache.CloudCacheUfc

	user := models.BaseConf.Mysql.User
	pwd := models.BaseConf.Mysql.Password
	dbname := models.BaseConf.Mysql.Dbname
	server := models.BaseConf.Mysql.Server
	err = utils.RegisterUfcadminDB(user, pwd, dbname, server)
	if err != nil {
		fmt.Println("ufc-admin-go init db error not nil, err:", err)
		golog.Error("ufc-admin-go init db error not nil, err: %v", err)
		os.Exit(-1)
	}

	//generate seed
	seed := time.Now().UnixNano()
	rand.Seed(seed)

	//start server
	port := models.BaseConf.Server.HttpPort
	fw := framework.New()
	router.SetRouter(fw)
	golog.Info("[message:ufc-admin-go server start listen:" + port + "]")
	fw.Run(":" + port)
}

func setupSignal() {
	c := make(chan os.Signal, 1)

	signal.Notify(c, syscall.SIGINT)
	signal.Notify(c, syscall.SIGHUP)
	signal.Notify(c, syscall.SIGUSR1)
	signal.Notify(c, syscall.SIGUSR2)

	signal.Notify(c, syscall.SIGTTIN)
	signal.Notify(c, syscall.SIGTTOU)

	go func() {
		for sig := range c {
			switch sig {
			case syscall.SIGUSR1:
				f, err := os.Create("ufc-admin-go.prof")
				if err != nil {
					golog.Error("%v", err)
				}
				err = pprof.StartCPUProfile(f)
				if err != nil {
					golog.Error("prof failed, error is %v", err)
				}

			case syscall.SIGUSR2:
				pprof.StopCPUProfile()

			case syscall.SIGTTIN:
				golog.SetLevel(golog.GetLevel() + 1)
			case syscall.SIGTTOU:
				golog.SetLevel(golog.GetLevel() - 1)

			case syscall.SIGHUP:
				golog.Info("SIGHUP")
				golog.ReOpen("")

			case syscall.SIGINT:
				golog.Error("SIGINT")
				os.Exit(0)
			}
		}
	}()
}
