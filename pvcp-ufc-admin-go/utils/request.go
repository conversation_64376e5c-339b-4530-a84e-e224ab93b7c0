package utils

import (
	"encoding/json"
	"fmt"
	"strconv"
)

type UfcAdminRequest struct {
	Method     string
	BodyString string
	BodyByte   []byte
}

func (r *UfcAdminRequest) GetParamInt(key string) int {
	var v interface{}
	var params map[string]interface{}
	params, err := r.GetParamMap()
	if err != nil {
		return 0
	}

	v = params[key]
	switch result := v.(type) {
	case int:
		return result
	case float64:
		return int(result)
	case string:
		re, _ := strconv.Atoi(result)
		return re
	}

	return 0
}

func (r *UfcAdminRequest) GetMethod() string {
	return r.Method
}

func (r *UfcAdminRequest) GetParamStr(key string) string {
	var v interface{}
	var params map[string]interface{}
	params, err := r.GetParamMap()
	if err != nil {
		return ""
	}

	v = params[key]
	switch result := v.(type) {
	case int:
		return strconv.Itoa(result)
	case string:
		return result
	case []byte:
		return string(result)
	default:
		if v != nil {
			return fmt.Sprint(result)
		}
	}

	return ""
}

func (r *UfcAdminRequest) GetParamMap() (map[string]interface{}, error) {
	var def, params map[string]interface{}
	err := json.Unmarshal(r.BodyByte, &params)
	if err != nil {
		return def, err
	}

	return params, nil
}
