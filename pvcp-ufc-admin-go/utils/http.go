package utils

import (
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const (
	RETRY_TIME             = 3
	CONNECT_TIME_OUT_MS    = time.Duration(2000)
	READ_WRITE_TIME_OUT_MS = time.Duration(10000)
	POOL_NUM               = 10
)

func HttpGet(url string) (res httpclient.HttpResponse, err error) {
	header := make(map[string]string)
	opts := make(map[string]string)

	opts["DisableFollowLocation"] = "0"
	opts["DisableCompression"] = "0"

	for i := 0; i < RETRY_TIME; i++ {
		res, err = httpclient.Get(url, header, CONNECT_TIME_OUT_MS, READ_WRITE_TIME_OUT_MS, opts)
		if err == nil {
			break
		}
	}

	return
}
