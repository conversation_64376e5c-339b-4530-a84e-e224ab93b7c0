package utils

import (
	"errors"
	"fmt"
)

type UfcadminError struct {
	ErrCode int
	ErrMsg  string
}

func MergeError(errs ...error) error {
	if len(errs) == 0 {
		return nil
	}
	var errMsg string
	for i, err := range errs {
		errMsg += fmt.Sprintf("{err %d: %v}", i, err)
	}
	return errors.New(errMsg)
}

func (e *UfcadminError) Error() string {
	return fmt.Sprintf("\"error_code\": \"%d\" \"error_msg\":\"%s\"}", e.ErrCode, e.ErrMsg)
}

// Various errors poms meta might return. Can change between driver versions.
var (
	Success = UfcadminError{200, "success"}

	//param invalid/missing
	ErrDoubleEndsParamToServiceMissing = UfcadminError{400, "param to_service missing"}
	ErrDoubleEndsParamForceMissing     = UfcadminError{400, "param force missing"}
	ErrDoubleEndsParamIpsMissing       = UfcadminError{400, "param ips missing"}
	ErrDoubleEndsParamIpsNotString     = UfcadminError{400, "param ips not string"}
	ErrDoubleEndsParamIdcMissing       = UfcadminError{400, "param idc missing"}
	ErrDoubleEndsParamIdcNotValid      = UfcadminError{400, "param idc not valid"}

	ErrBodyParamMissing    = UfcadminError{400, "param missing"}
	ErrServiceParamMissing = UfcadminError{400, "service missing"}
	ErrBodyJSONFormatError = UfcadminError{400, "body format json error"}
	ErrMustParamError      = UfcadminError{400, "must param error"}
	ErrBodyParamJSONError  = UfcadminError{400, "must param json error"}

	ErrServiceIsExist  = UfcadminError{400, "service is exists"}
	ErrServiceNotExist = UfcadminError{400, "service not exists"}
	ErrBnsNotExist     = UfcadminError{400, "bns not exists"}
	ErrIndexTooLarge   = UfcadminError{400, "Stream index reaches the upper limit"}

	ErrDBConnectFailed = UfcadminError{503, "db connect failed"}
	ErrDBResultError   = UfcadminError{503, "db result error"}

	//other errors
	ErrJSONEncodeError = UfcadminError{503, "json encode error"}

	ErrDropRequest = UfcadminError{400, "random drop request"}

	ErrRuleMapNotExistError = UfcadminError{503, "conf get map error"}
	ErrIplistError          = UfcadminError{400, "get small flow ip list failed"}
	ErrIplistFormatError    = UfcadminError{400, "small flow ip list format error"}
	ErrIplistExist          = UfcadminError{400, "small flow checker is running"}
	//unknown error
	ErrUnknown = UfcadminError{400, "Unknown error"}
)
