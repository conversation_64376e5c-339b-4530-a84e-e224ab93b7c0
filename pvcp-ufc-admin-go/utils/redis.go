package utils

import (
	"sync"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/redigo/redis"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/ufc"
)

const (
	//should retry three times.
	RETRY_TIMES         = 3
	MAX_IDLE_CONNECTION = 10
	CONNECT_TIMEOUT     = 10 * time.Second
	READ_TIMEOUT        = 30 * time.Second
	WRITE_TIMEOUT       = 30 * time.Second

	EXPIRE_TTL_TIME_SECOND      = "EX"
	EXPIRE_TTL_TIME_MILLISECOND = "PX"
	SET_IF_NOT_EXIST            = "NX"
	SET_ONLY_EXIST              = "XX"
)

var CloudCacheUfc string

var (
	_redisPool *redis.Pool
	once       = new(sync.Once)
)

func pool() {
	_redisPool = &redis.Pool{Dial: dial, MaxIdle: MAX_IDLE_CONNECTION, TestOnBorrow: testOnBorrow}
}

var index int = 0

// connect redis server via ufc bypass mode
func dial() (redis.Conn, error) {
	headers := make(map[string]string)
	logid := GetLogid()
	callid := "1"
	uc, err := ufc.NewUfcBypassContext("self_name", CloudCacheUfc, logid, callid, headers)
	if err != nil {
		return nil, err
	}

	_, err = uc.GetServer()
	if err != nil {
		return nil, err
	}
	addr := uc.Host()

	//addr = "127.0.0.1:6379"
	//addr = "sh01-pcs-dev00.sh01:6379"
	conn, err := redis.Dial("tcp", addr, redis.DialConnectTimeout(CONNECT_TIMEOUT), redis.DialReadTimeout(READ_TIMEOUT), redis.DialWriteTimeout(WRITE_TIMEOUT))
	return conn, err
}

func testOnBorrow(c redis.Conn, t time.Time) error {
	_, err := c.Do("PING")
	return err
}

func getConn() redis.Conn {
	once.Do(pool)

	return _redisPool.Get()
}

func GetRedisConn() redis.Conn {
	return getConn()

}

func SetCache(key string, content interface{}) bool {
	var ok bool
	var err error
	var res string
	ok = false

	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}

		res, err = redis.String(c.Do("SET", key, content))
		if err != nil {
			continue
		}
		c.Close()

		if res == "OK" {
			ok = true
			break
		}
	}

	return ok
}

func MsetCache(params ...interface{}) bool {
	var ok bool
	var err error
	var res string
	ok = false

	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}
		res, err = redis.String(c.Do("MSET", params...))
		if err != nil {
			continue
		}

		if res == "OK" {
			ok = true
			break
		}
	}

	return ok
}

func Hset(key, field, value string) bool {
	var ok bool
	var err error
	var res int64
	ok = false
	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}
		res, err = redis.Int64(c.Do("HSET", key, field, value))
		if err != nil {
			golog.Error("redis HSET error, err=%v", err)
			continue
		}

		if res == 1 {
			ok = true
			break
		}
	}

	return ok
}

func HMSet(key string, slice ...string) bool {
	var ok bool
	var err error
	var res string
	ok = false
	if len(slice)%2 != 0 {
		golog.Error("[redis HMSET error, slice length is not even] [key: %v] [slice: %v]", key, slice)
		return false
	}

	var args []interface{}
	args = append(args, key)
	for _, item := range slice {
		args = append(args, item)
	}

	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}
		res, err = redis.String(c.Do("HMSET", args...))
		if err != nil {
			golog.Error("redis HMSET error, err=%v, args=%v, remote_addr=%v", err, args, c.RemoteAddr())
			continue
		}

		if res == "OK" {
			ok = true
			break
		}
	}

	return ok
}

func Hgetall(key string) (map[string]string, error) {
	var err error
	var res map[string]string
	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}
		res, err = redis.StringMap(c.Do("HGETALL", key))
		if err != nil {
			continue
		}
	}

	return res, err
}

func Smembers(key string) ([]string, error) {
	var err error
	var res []string
	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}
		res, err = redis.Strings(c.Do("Smembers", key))
		if err != nil {
			continue
		}
	}

	return res, err
}

func Exists(key string) bool {
	var err error
	var res bool
	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}
		res, err = redis.Bool(c.Do("EXISTS", key))
		if err != nil {
			continue
		}
	}

	return res
}

func GetCache(key string) string {
	var v string
	var err error
	v = ""
	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}

		v, err = redis.String(c.Do("GET", key))
		if err == nil {
			break
		}
		golog.Error("[i: %d] [redis GET error] [key: %s] [v: %s] [err: %v] [addr: %s]", i, key, v, err, c.RemoteAddr())
	}

	return v
}

func GetIntCache(key string) int64 {
	var v int64 = -1
	var err error
	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}

		v, err = redis.Int64(c.Do("GET", key))
		if err == nil {
			break
		}
	}

	return v
}

func SetIncr(key string) int64 {
	var v int64
	var err error
	v = 0

	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}

		v, err = redis.Int64(c.Do("INCR", key))
		if err == nil {
			break
		}
	}

	return v
}

func DelCache(key string) int {
	var v int
	var err error
	v = 0
	for i := 0; i < 3; i++ {
		c := GetRedisConn()
		if c == nil {
			continue
		}

		v, err = redis.Int(c.Do("DEL", key))
		if err == nil {
			break
		}
	}

	return v
}
