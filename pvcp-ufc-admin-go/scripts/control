#!/bin/sh
deployDir='/home/<USER>/ufcadmin'

version() {
    version=`md5sum /home/<USER>/ufcadmin/bin/ufcadmin`
    echo $version
    exit 0
}

status() {
    is_running=`ps xu | grep "./ufcadmin" | grep -v grep | wc -l`
    if [[ $is_running -eq 0 ]]; then
        exit 1
    else
        exit 0 #ok
    fi
}

stop() {
    for pid in $(ps -ef | grep './ufcadmin' | grep -vE 'grep|stop' | awk '{print $2}'); do
        kill -9 $pid
    done

    sleep 0.5

    pid_num=`ps -ef | grep './ufcadmin' | grep -v grep | wc -l`
    if [ $pid_num -gt 0 ]
    then
        for pid in $(ps -ef | grep './ufcadmin' | grep -vE 'grep|stop' | awk '{print $2}'); do
            kill -9 $pid
            sleep 0.5
        done
    fi
}

start() {
    cd /home/<USER>/ufcadmin/bin ; ./ufcadmin ../conf/base.toml &
    sleep 5
}

restart() {
    stop
    sleep 0.5
    start
}

case "$1" in
    version)
        version
    ;;
    status)
        status
    ;;
    restart)
        restart
    ;;
    stop)
        stop
    ;;
    start)
        restart
    ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|version}"
    ;;
esac