/*
 * File         : router.go
 * Author       : huanghaifeng02
 * Date         : 2018-03-21 10:16:02
 * Last modified: 2015-03-29 14:25:57
 */
package router

import (
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/framework"
	ufcadmin "icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/controller"
)

func SetRouter(fw *framework.Framework) {
	fw.AddRouterByQsKey("method")
	fw.AddAutoRouter("/rest/2.0/pvcp/ufcadmin", &ufcadmin.UfcadminController{})

	fw.NotFound(ufcadmin.NotFound)
}
