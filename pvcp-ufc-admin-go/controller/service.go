package ufcadmin

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/simplejson"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/models"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

func (p *UfcadminController) ConfigAdd() error {
	var req *utils.UfcAdminRequest
	var err error

	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	Mod := &models.ServiceModel{
		ServiceTable: models.BaseConf.Mysql.ServiceTable,
		StreamTable:  models.BaseConf.Mysql.StreamTable,
	}

	// 请求初始化
	req = p.CreateRequest(&p.FwController)

	err = p.CheckParamValid(req)
	if err != nil {
		golog.Error("[msg: check param valid failed] [err: %s]", err.Error())
		return err
	}

	serviceName := req.GetParamStr("service_name")
	if serviceName == "" {
		err = &utils.ErrServiceParamMissing
		golog.Error("[msg: param service_name missing] [err: %s]", err.Error())
		return err
	}
	serviceName = strings.TrimSpace(serviceName)

	// 检测是否已经存在
	isExist := Mod.CheckIsExist(serviceName)
	if isExist {
		err = &utils.ErrServiceIsExist
		golog.Error("[msg: service %s already exist] [err: %s]", serviceName, err.Error())
		return err
	}

	config, err := req.GetParamMap()
	if err != nil {
		golog.Error("[msg: get param map err] [err: %s]", err.Error())
		return err
	}

	// ufc name 和 bns list trimspace
	config["service_name"] = serviceName

	bnsListMap, ok := config["bns_list"].(map[string]any)
	if !ok {
		err = &utils.ErrMustParamError
		golog.Error("[msg: bns_list %s param error] [err: %s]", serviceName, err.Error())
		return err
	}

	for bns, v := range bnsListMap {
		newbns := strings.TrimSpace(bns)
		bnsListMap[newbns] = v
	}
	config["bns_list"] = bnsListMap
	// trimspace end

	config["mtime"] = models.GetTime()
	id := Mod.Add(config) // 先写 service 表
	if id < 1 {
		err = &utils.ErrDBResultError
		golog.Error("[msg: add service failed] [err: %s]", err.Error())
		return err
	}

	c := &CloudCache{Service: serviceName}
	// 抛出错误让调用方知晓，然后手动去设置 bns
	err = c.ForIdcAdd(config)
	if err != nil {
		err = &utils.UfcadminError{ErrCode: 500, ErrMsg: err.Error()}
		return err
	}

	data := simplejson.New()
	data.Set("id", id)

	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}

func (p *UfcadminController) ConfigUpdate() error {
	var req *utils.UfcAdminRequest
	var err error

	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	Mod := &models.ServiceModel{
		ServiceTable: models.BaseConf.Mysql.ServiceTable,
		StreamTable:  models.BaseConf.Mysql.StreamTable,
	}

	// 请求初始化
	req = p.CreateRequest(&p.FwController)

	err = p.CheckParamValid(req)
	if err != nil {
		golog.Error("[msg: check param valid failed] [err: %s]", err.Error())
		return err
	}

	serviceName := req.GetParamStr("service_name")
	if serviceName == "" {
		err = &utils.ErrServiceParamMissing
		golog.Error("[msg: param service_name missing] [err: %s]", err.Error())
		return err
	}
	serviceName = strings.TrimSpace(serviceName)

	// 检测之前是否已经有小流量checker在跑
	existIps := models.GetSmallFlowIps(serviceName)

	// 之前已经有小流量checker在跑，返回失败
	if len(existIps) != 0 {
		err = &utils.ErrIplistExist
		golog.Error("[msg: ip list exist] [err: %s]", err.Error())
		return err
	}

	smallFlowIP := req.GetParamStr("smallflowips")

	// 检测是否已经存在
	isExist := Mod.CheckIsExist(serviceName)
	if !isExist {
		err = &utils.ErrServiceNotExist
		golog.Error("[msg: service %s not exist] [err: %s]", serviceName, err.Error())
		return err
	}

	config, err := req.GetParamMap()
	if err != nil {
		golog.Error("[msg: get param map err] [err: %s]", err.Error())
		return err
	}

	config["mtime"] = models.GetTime()

	// 如果是小流量请求不修改mysql中的数据
	if smallFlowIP == "" {
		ok := Mod.Update(serviceName, config)
		if !ok {
			err = &utils.ErrDBResultError
			golog.Error("[msg: update service failed] [err: %s]", err.Error())
			return err
		}
	}
	// 是小流量请求把机器加入小流量池
	if smallFlowIP != "" {
		err = models.ForIdcAdd(serviceName, smallFlowIP)
		if err != nil {
			golog.Error("[msg: update service failed] [err: %s]", err.Error())
			err = &utils.UfcadminError{ErrCode: 500, ErrMsg: err.Error()}
			return err
		}
	}

	c := &CloudCache{Service: serviceName}
	err = c.ForIdcUpdate(config)
	if err != nil {
		golog.Error("[msg: update service failed] [err: %s]", err.Error())
		err = &utils.UfcadminError{ErrCode: 500, ErrMsg: err.Error()}
		return err
	}

	data := simplejson.New()
	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}

func (p *UfcadminController) ConfigQuery() error {
	var err error
	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	// 请求初始化
	req := p.CreateRequest(&p.FwController)

	Mod := &models.ServiceModel{
		ServiceTable: models.BaseConf.Mysql.ServiceTable,
		StreamTable:  models.BaseConf.Mysql.StreamTable,
	}

	key := req.GetParamStr("key")
	if key == "" {
		err = &utils.ErrMustParamError
		return err
	}

	value := req.GetParamStr("value")
	if value == "" {
		err = &utils.ErrMustParamError
		return err
	}

	res, err := Mod.Query(key, value)
	if err != nil {
		return err
	}

	data, err := json.Marshal(res)
	if err != nil {
		return err
	}

	js, err := simplejson.NewJson(data)
	if err != nil {
		return err
	}
	body := p.CreateBody(js)
	p.Echo(200, body)
	return nil
}

func (p *UfcadminController) ConfigQueryAll() error {
	var err error

	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	// 请求初始化
	req := p.CreateRequest(&p.FwController)

	Mod := &models.ServiceModel{
		ServiceTable: models.BaseConf.Mysql.ServiceTable,
		StreamTable:  models.BaseConf.Mysql.StreamTable,
	}

	total, err := Mod.QueryTotal()
	if err != nil {
		return err
	}

	start := req.GetParamInt("start")
	size := req.GetParamInt("size")
	if size < 1 {
		size = 10
	}

	res, err := Mod.QueryAll(start, size)
	if err != nil {
		return err
	}

	data := simplejson.New()
	data.Set("total", total)
	data.Set("list", res)

	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}

func (p *UfcadminController) TotalResourceGet() error {
	var err error
	var bnsNames []string

	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	bnsName := p.Ctx.Input.Query("bns_name")
	if bnsName == "" {
		err = &utils.ErrMustParamError
		return err
	}

	var day string
	day = p.Ctx.Input.Query("day")
	if day == "" {
		day = "7"
	}

	dayInt, err := strconv.Atoi(day)
	if err != nil {
		return &utils.UfcadminError{ErrCode: 400, ErrMsg: "day not a int"}
	}

	if strings.Contains(bnsName, "group.") {
		bnsNames = models.GetBnsByGroup(bnsName)
	} else {
		bnsNames = append(bnsNames, bnsName)
	}
	EndTime := time.Now()
	d, err := time.ParseDuration("-24h")
	if err != nil {
		return err
	}
	StartTime := EndTime.Add(time.Duration(dayInt) * d)
	data, err := models.GetResourceData(bnsNames, StartTime, EndTime)

	if err != nil {
		return err
	}

	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}

func (p *UfcadminController) OneDayResourceGet() error {
	var err error
	var bnsNames []string

	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	bnsName := p.Ctx.Input.Query("bns_name")
	if bnsName == "" {
		err = &utils.ErrMustParamError
		return err
	}

	var day string
	day = p.Ctx.Input.Query("day")
	if day == "" {
		err = &utils.ErrMustParamError
		return err
	}

	dayInt, err := strconv.Atoi(day)
	if err != nil {
		return &utils.UfcadminError{ErrCode: 400, ErrMsg: "day not a int"}
	}

	if strings.Contains(bnsName, "group.") {
		bnsNames = models.GetBnsByGroup(bnsName)
	} else {
		bnsNames = append(bnsNames, bnsName)
	}

	NowTime := time.Now()
	d, err := time.ParseDuration("-24h")
	if err != nil {
		return err
	}

	EndTime := NowTime.Add(time.Duration(dayInt-1) * d)
	StartTime := NowTime.Add(time.Duration(dayInt) * d)
	data, err := models.GetResourceData(bnsNames, StartTime, EndTime)

	if err != nil {
		return err
	}

	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}

// 注册新的走stream模式的service
// 可以只指定to_service, 也可以同时指定from service 和to service 但是不能只指定from service
// 只有to service的index信息会写到mysql里面，
func (p *UfcadminController) StreamAdd() error {
	var err error

	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	// to service 必须要有
	toService := p.Ctx.Input.Query("to_service")
	if toService == "" {
		err = &utils.ErrMustParamError
		return err
	}
	// from service 可以没有
	fromService := p.Ctx.Input.Query("from_service")

	if fromService == "" {
		// 只注册to service
		// 先看一下mysql里面有没有
		Mod := &models.ServiceModel{
			ServiceTable: models.BaseConf.Mysql.ServiceTable,
			StreamTable:  models.BaseConf.Mysql.StreamTable,
		}

		isExist := Mod.CheckStreamIsExist(toService)
		if isExist {
			golog.Warn("service %s is in stream table yet", toService)
			err = &utils.ErrServiceIsExist
			return err
		}
		// 先把数据写到mysql里面
		index := Mod.AddStream(toService)
		// 数据库插入错误
		if index < 1 {
			golog.Warn("service %s add data to db error", toService)
			return &utils.ErrDBResultError
		}

		// index最多只能有11位，超出范围返回错误
		if index > 0x7ff {
			golog.Warn("service %s is in stream table yet", toService)
			err = &utils.ErrIndexTooLarge
			return err
		}

		// 把数据写到redis里面
		c := &CloudCache{Service: toService}
		c.ForIdcAddStream(uint32(index), 0, "")
	} else {
		// 注册的是链路的映射关系
		// 先看一下to_service 注册没有
		Mod := &models.ServiceModel{
			ServiceTable: models.BaseConf.Mysql.ServiceTable,
			StreamTable:  models.BaseConf.Mysql.StreamTable,
		}

		isExist := Mod.CheckStreamIsExist(toService)
		if !isExist {
			golog.Warn("service %s is not in stream table yet", toService)
			err = &utils.ErrServiceNotExist
			return err
		}
		// 获取to service 对应的id
		id := Mod.GetStreamID(toService)
		// 获取指定下游上次分配到的index
		index := Mod.UpdateStreamIndex(toService)
		if index < 0 {
			golog.Warn("service %s add data to db error", toService)
			return &utils.ErrDBResultError
		}
		// 把数据写到redis里面
		c := &CloudCache{Service: toService}
		c.ForIdcAddStream(uint32(id), uint32(index), fromService)
	}

	data := simplejson.New()
	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}

func (p *UfcadminController) ConfigDelete() error {
	var err error

	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	key := p.Ctx.Input.Query("key")
	if key == "" {
		err = &utils.ErrMustParamError
		return err
	}

	if key != "service" && key != "bns" {
		err = &utils.ErrMustParamError
		return err
	}

	value := p.Ctx.Input.Query("value")
	if value == "" {
		err = &utils.ErrMustParamError
		return err
	}

	if key == "service" {
		// 如果删除的是service检查service是否存在
		Mod := &models.ServiceModel{
			ServiceTable: models.BaseConf.Mysql.ServiceTable,
			StreamTable:  models.BaseConf.Mysql.StreamTable,
		}

		isExist := Mod.CheckIsExist(value)
		if !isExist {
			// 即使service已经不在mysql中也不反回错误
			// 防止出现某些idc redis删不掉，可以支持重复请求接口删除
			golog.Warn("service %s is not in mysql yet", value)
		}
		// 先删mysql里面的数据
		_, err = Mod.Delete(value)
		if err != nil {
			golog.Warn("delte service %s from db failed, error is %v", value, err)
			err = &utils.ErrDBResultError
			return err
		}
		// 再删redis
		c := &CloudCache{Service: value}
		err = c.ForIdcDeleteService()
		if err != nil {
			err = &utils.UfcadminError{ErrCode: 500, ErrMsg: err.Error()}
			return err
		}
	}

	if key == "bns" {
		// 如果删除的是bns检查redis中bns是否存在
		// 选一个idc做为基准检查，一般选北京
		baseIdc := models.BaseConf.Idc.Base
		b := &models.BnsCache{Idc: baseIdc, Bns: value}
		isExist := b.IsExit("bns")
		if !isExist {
			// 同样的即使base idc里面没有这个bns也不返回错误
			// 防止出现某些idc redis删不掉，可以支持重复请求接口删除
		}
		// 删除redis中的数据
		c := &CloudCache{Service: value}
		err = c.ForIdcDeleteBns(value)
		if err != nil {
			err = &utils.UfcadminError{ErrCode: 500, ErrMsg: err.Error()}
			return err
		}
	}

	data := simplejson.New()
	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}
