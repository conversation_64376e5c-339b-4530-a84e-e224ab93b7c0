package ufcadmin

import (
	"encoding/json"
	"errors"
	"fmt"
	"sync"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/models"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

type CloudCache struct {
	Service string
}

func (m *CloudCache) ForIdcAdd(config map[string]interface{}) error {
	idcs := m.GetIdcs()

	serviceCache := &models.ServiceCache{Service: m.Service}

	var wg sync.WaitGroup
	wg.Add(len(idcs))
	for _, idc := range idcs {
		idc := idc
		args, idcEnable := m.GetCacheParams("", config)
		go func() {
			defer wg.Done()
			err := serviceCache.Add(idc, args, idcEnable)
			if err != nil {
				serviceCache.AppendErrorWithLock(err)
			}
		}()
	}
	wg.Wait()

	if len(serviceCache.Errors) != 0 {
		err := utils.MergeError(serviceCache.Errors...)
		return err
	}

	return nil
}

// 向各个机房添加stream映射关系
func (m *CloudCache) ForIdcAddStream(toServiceIndex uint32, fromServiceIndex uint32, fromService string) {
	idcs := m.GetIdcs()
	for _, idc := range idcs {
		s := &models.StreamCache{Service: m.Service}
		go s.AddStream(idc, toServiceIndex, fromServiceIndex, fromService)
	}

	return
}

func (m *CloudCache) ForIdcDeleteService() error {
	idcs := m.GetIdcs()

	serviceCache := &models.ServiceCache{Service: m.Service}

	var wg sync.WaitGroup
	wg.Add(len(idcs))
	for _, idc := range idcs {
		idc := idc
		go func() {
			defer wg.Done()
			if err := serviceCache.Delete(idc); err != nil {
				serviceCache.AppendErrorWithLock(err)
			}

		}()
	}
	wg.Wait()

	if len(serviceCache.Errors) != 0 {
		err := utils.MergeError(serviceCache.Errors...)
		return err
	}
	return nil
}

func (m *CloudCache) ForIdcDeleteBns(bns string) error {
	idcs := m.GetIdcs()

	serviceCache := &models.ServiceCache{Service: m.Service}

	var wg sync.WaitGroup
	wg.Add(len(idcs))
	for _, idc := range idcs {
		idc := idc
		b := &models.BnsCache{Idc: idc, Bns: bns}
		go func() {
			defer wg.Done()

			b.DeleteBns() // delete 完毕后会存在 err
			if len(b.Errors) != 0 {
				for _, err := range b.Errors {
					serviceCache.AppendErrorWithLock(err)
				}
			}
		}()
	}
	wg.Wait()

	if len(serviceCache.Errors) != 0 {
		return utils.MergeError(serviceCache.Errors...)
	}
	return nil
}

func (m *CloudCache) ForIdcUpdate(config map[string]interface{}) error {
	idcs := m.GetIdcs()
	serviceCache := &models.ServiceCache{Service: m.Service}

	var wg sync.WaitGroup
	wg.Add(len(idcs))
	for _, idc := range idcs {
		idc := idc
		args, idcEnable := m.GetCacheParams(idc, config)
		go func() {
			defer wg.Done()
			err := serviceCache.Update(idc, args, idcEnable)
			if err != nil {
				serviceCache.AppendErrorWithLock(err)
			}
		}()
	}
	wg.Wait()

	if len(serviceCache.Errors) != 0 {
		err := utils.MergeError(serviceCache.Errors...)
		return err
	}

	return nil
}

func (m *CloudCache) GetIdcs() []string {
	idcs := models.BaseConf.Idc.Names
	return idcs
}

func (m *CloudCache) GetCacheParams(idc string, config map[string]interface{}) (map[string]interface{}, bool) {
	var args, small map[string]interface{}
	var smallFlow []interface{}
	var idcEnable bool

	idcEnable = false

	args = make(map[string]interface{})
	small = make(map[string]interface{})

	for k, v := range models.CacheColumnMap {
		vv := config[v]
		if vv == nil {
			continue
		}

		if tmp, ok := vv.(float64); ok {
			if tmp < 0 {
				continue
			}
		}

		args[k] = config[v]
	}

	if idc != "" {
		pre := models.GetPrefix(idc)
		key := fmt.Sprintf("%s-service-%s", pre, m.Service)
		golog.Info("[Idc = " + idc + "] [Method = Set_Update_Cache] [Ori_Config_key = " + key + "]")

		content := utils.GetCache(key)
		golog.Info("[Idc = " + idc + "] [Method = Set_Update_Cache] [Ori_Config = " + string(content) + "]")

		var oriConfig map[string]interface{}
		err := json.Unmarshal([]byte(content), &oriConfig)
		if err == nil && oriConfig != nil {
			for k, v := range oriConfig {
				_, ok := args[k]
				if !ok {
					args[k] = v
				}
			}
		}
	}

	var js map[string]interface{}
	for k, v := range models.CacheJSONColumnMap {
		str, ok := config[v].(string)
		if !ok {
			continue
		}

		if str == "" || str == "{}" {
			delete(args, k)
			continue
		}

		js = make(map[string]interface{})
		err := json.Unmarshal([]byte(str), &js)
		if err == nil {
			args[k] = js
		}
	}

	if args["idc_map"] != nil {
		idcEnable = true
	}

	// small flow
	bns, err := m.GetJSONParam(config, "small_flow_bns_list")
	if err == nil {
		small["bns_list"] = bns

		// idc_map
		idcMap, err := m.GetJSONParam(config, "small_flow_idc_map")
		if err == nil {
			idcEnable = true
			small["idc_map"] = idcMap
		}

		// hash range
		hash, err := m.GetJSONParam(config, "small_flow_hash_range")
		if err == nil {
			small["hash_range"] = hash
			args["small_flow"] = smallFlow
			args["small_flow"] = append(smallFlow, small)
		}

		// small_list
		smallList, err := m.GetJSONParam(config, "small_flow_small_list")
		if err == nil {
			small["small_list"] = smallList
			args["small_flow"] = smallFlow
			args["small_flow"] = append(smallFlow, small)
		}
	} else {
		delete(args, "small_flow")
	}

	// advanced route
	isConfigExist := true
	advancedRouteRuleConfig, err := m.GetJSONParam(config, "advanced_route_rule") // 获取高级路由配置
	if err != nil {
		isConfigExist = false
	}
	advancedRouteRuleApp, err := m.GetJSONParam(config, "advanced_route_app")
	if err != nil {
		isConfigExist = false
	}

	if !isConfigExist {
		delete(args, "advanced_route")
	} else {
		advancedRoute := make(map[string]map[string]interface{})
		advancedRoute["route_rule"] = advancedRouteRuleConfig
		advancedRoute["route_app"] = advancedRouteRuleApp
		args["advanced_route"] = advancedRoute
	}

	return args, idcEnable
}

func (m *CloudCache) GetJSONParam(config map[string]interface{}, key string) (map[string]interface{}, error) {
	var param map[string]interface{}
	str, ok := config[key].(string)
	if !ok || str == "" {
		return param, errors.New("key is not map")
	}

	if str == "{}" {
		return param, errors.New("key is not map")
	}

	err := json.Unmarshal([]byte(str), &param)
	if err != nil {
		return param, errors.New("new json error")
	}

	return param, nil
}
