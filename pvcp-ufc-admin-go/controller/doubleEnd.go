package ufcadmin

import (
	"fmt"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/simplejson"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/models"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

// 双端配置初始化，当双端配置不存在时 incre prefix-configs-cnt, set prefix-configs-index double_ends
func (p *UfcadminController) ConfigInitDoubleEnds() {
	var err error
	defer func() {
		if err != nil {
			e, ok := err.(*utils.UfcadminError)
			if !ok {
				e = &utils.UfcadminError{ErrCode: 400, ErrMsg: err.Error()}
				err = e
			}
			p.<PERSON>rror(err)
		}
	}()

	idc := p.Ctx.Input.Query("idc")
	if idc == "" {
		err = &utils.ErrDoubleEndsParamIdcMissing
		golog.Warn("[init double ends failed] [param idc is not exist]")
		return
	}
	rewrite := false
	rewriteStr := p.Ctx.Input.Query("rewrite")
	if rewriteStr == "true" {
		rewrite = true
	}

	allIDC := false
	idcValid := false
	if idc == "all" {
		allIDC = true
		idcValid = true
	} else {
		for _, idcName := range models.BaseConf.Idc.Names {
			if idcName == idc {
				idcValid = true
				break
			}
		}
	}

	if !idcValid {
		golog.Warn("[init double ends] [param idc is illegal] [idc: %s]", idc)
		err = &utils.ErrDoubleEndsParamIdcNotValid
		return
	}

	// add all idc
	if allIDC {
		var idcList []string
		idcList, err = models.InitAllIdcConfigsDoubleEnds(rewrite)
		if err != nil {
			golog.Warn("[open double ends] [add all idc double ends error] [err: %v]", err)
			return
		}
		data := simplejson.New()
		data.Set("idc", idcList)

		body := p.CreateBody(data)
		p.Echo(200, body)
		return
	}

	// add one idc
	err = models.InitOneIdcConfigsDoubleEnds(idc, rewrite)
	if err != nil {
		golog.Warn("[open double ends] [add one idc double ends error] [idc: %s] [err: %v]", idc, err)
		return
	}

	data := simplejson.New()
	data.Set("idc", idc)
	body := p.CreateBody(data)
	p.Echo(200, body)
}

// 小流量开启双端接口
func (p *UfcadminController) ConfigOpenDoubleEndsSmallflow() {
	var err error
	defer func() {
		if err != nil {
			e, ok := err.(*utils.UfcadminError)
			if !ok {
				e = &utils.UfcadminError{ErrCode: 400, ErrMsg: err.Error()}
				err = e
			}
			p.EchoError(err)
		}
	}()

	req := p.CreateRequest(&p.FwController)

	paramMap, err := req.GetParamMap()
	if err != nil {
		golog.Warn("[open double ends] [get param map error] [err: %v]", err)
		return
	}

	toService, ok := paramMap["to_service"].(string)
	if !ok {
		err = &utils.ErrDoubleEndsParamToServiceMissing
		golog.Warn("[open double ends] [param to_service is not exist]")
		return
	}
	var ipsStrSlice []string
	ipsInterface, ok := paramMap["ips"].([]interface{})
	if !ok {
		err = &utils.ErrDoubleEndsParamIpsMissing
		golog.Warn("[open double ends] [param ips is not exist]")
		return
	}
	for _, ip := range ipsInterface {
		ipStr, ok := ip.(string)
		if !ok {
			err = &utils.ErrDoubleEndsParamIpsNotString
			golog.Warn("[open double ends] [param ips is not string]")
			return
		}
		ipsStrSlice = append(ipsStrSlice, ipStr)
	}

	err = models.AddDoubleEndsSmallflow(toService, ipsStrSlice)
	if err != nil {
		golog.Warn("[open double ends] [add smallflow error] [err: %v]", err)
		return
	}
	data := simplejson.New()
	data.Set("to_service", toService)
	data.Set("ips", ipsStrSlice)
	body := p.CreateBody(data)
	p.Echo(200, body)
}

func (p *UfcadminController) ConfigOpenDoubleEndsFull() {
	var err error
	defer func() {
		if err != nil {
			e, ok := err.(*utils.UfcadminError)
			if !ok {
				e = &utils.UfcadminError{ErrCode: 400, ErrMsg: err.Error()}
				err = e
			}
			p.EchoError(err)
		}
	}()

	req := p.CreateRequest(&p.FwController)

	paramMap, err := req.GetParamMap()
	if err != nil {
		golog.Warn("[open double ends] [get param map error] [err: %v]", err)
		return
	}
	toService, ok := paramMap["to_service"].(string)
	if !ok {
		err = &utils.ErrDoubleEndsParamToServiceMissing
		golog.Warn("[open double ends] [param to_service is not exist]")
		return
	}
	force, ok := paramMap["force"].(bool)
	if !ok {
		err = &utils.ErrDoubleEndsParamForceMissing
		golog.Warn("[open double ends] [param force is not exist]")
		return
	}

	err = models.AddDoubleEndsFull(toService, force)
	if err != nil {
		golog.Warn("[open double ends] [add full error] [err: %v]", err)
		return
	}

	data := simplejson.New()
	data.Set("to_service", toService)
	data.Set("force", force)
	body := p.CreateBody(data)
	p.Echo(200, body)
}

func (p *UfcadminController) ConfigOpenDoubleEndsRollback() {
	var err error
	defer func() {
		if err != nil {
			e, ok := err.(*utils.UfcadminError)
			if !ok {
				e = &utils.UfcadminError{ErrCode: 400, ErrMsg: err.Error()}
				err = e
			}
			p.EchoError(err)
		}
	}()

	req := p.CreateRequest(&p.FwController)

	paramMap, err := req.GetParamMap()
	if err != nil {
		golog.Warn("[open double ends] [get param map error] [err: %v]", err)
		return
	}
	toService, ok := paramMap["to_service"].(string)
	if !ok {
		err = &utils.ErrDoubleEndsParamToServiceMissing
		golog.Warn("[open double ends] [param to_service is not exist]")
		return
	}

	err = models.AddDoubleEndsRollback(toService)
	if err != nil {
		golog.Error("[add double ends rollback error] [err: %v]", err)
		return
	}

	data := simplejson.New()
	data.Set("to_service", toService)
	body := p.CreateBody(data)
	p.Echo(200, body)
}

// 全量开启双端：该接口暂废弃，被上述小流量接口、全量接口、回滚接口替代。后续稳定将该部分代码删除
/*
func (p *UfcadminController) ConfigOpenDoubleEnds() {
	var err error
	defer func() {
		if err != nil {
			e, ok := err.(*utils.UfcadminError)
			if !ok {
				e = &utils.UfcadminError{ErrCode: 400, ErrMsg: err.Error()}
				err = e
			}
			p.EchoError(err)
		}
	}()

	req := p.CreateRequest(&p.FwController)

	paramMap, err := req.GetParamMap()
	if err != nil {
		golog.Warn("[open double ends] [get param map error] [err: %v]", err)
		return
	}

	toService, ok := paramMap["to_service"].(string)
	if !ok {
		err = &utils.ErrDoubleEndsParamToServiceMissing
		golog.Warn("[open double ends] [param to_service is not exist]")
		return
	}

	idc, ok := paramMap["idc"].(string)
	if !ok {
		err = &utils.ErrDoubleEndsParamIdcMissing
		golog.Warn("[open double ends] [param idc is not exist]")
		return
	}

	allIDC := false
	idcValid := false
	if idc == "all" {
		allIDC = true
		idcValid = true
	} else {
		for _, idcName := range models.BaseConf.Idc.Names {
			if idcName == idc {
				idcValid = true
				break
			}
		}
	}

	if !idcValid {
		golog.Warn("[open double ends] [param idc is illegal] [idc: %s]", idc)
		err = fmt.Errorf("param idc is illegal")
		return
	}

	// add all idc
	if allIDC {
		list := make([]string, 0)
		list, err = models.AddAllIdcDoubleEnds(toService)
		if err != nil {
			golog.Warn("[open double ends] [add all idc double ends error] [err: %v]", err)
			return
		}
		data := simplejson.New()
		data.Set("idc", list)

		body := p.CreateBody(data)
		p.Echo(200, body)

		err = nil
		return
	}

	// add one idc
	err = models.AddOneIdcDoubleEnds(toService, idc)
	if err != nil {
		golog.Warn("[open double ends] [add one idc double ends error] [idc: %s] [err: %v]", idc, err)
		return
	}

	data := simplejson.New()
	data.Set("idc", idc)

	body := p.CreateBody(data)
	p.Echo(200, body)

	return
}
*/

func (p *UfcadminController) ConfigCloseDoubleEnds() {
	var err error
	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	req := p.CreateRequest(&p.FwController)

	paramMap, err := req.GetParamMap()
	if err != nil {
		golog.Warn("[open double ends] [get param map error] [err: %v]", err)
		return
	}

	// "beijing",
	// "yangquan",
	// "qingdao",
	// "shanghai",
	// "xian"
	toService, ok := paramMap["to_service"].(string)
	if !ok {
		err = &utils.ErrDoubleEndsParamToServiceMissing
		golog.Warn("[open double ends] [param to_service is not exist]")
		return
	}

	idc, ok := paramMap["idc"].(string)
	if !ok {
		err = &utils.ErrDoubleEndsParamIdcMissing
		golog.Warn("[open double ends] [param idc is not exist]")
		return
	}

	allIDC := false
	idcValid := false
	if idc == "all" {
		allIDC = true
		idcValid = true
	} else {
		for _, idcName := range models.BaseConf.Idc.Names {
			if idcName == idc {
				idcValid = true
				break
			}
		}
	}

	if !idcValid {
		golog.Warn("[close double ends] [param idc is illegal] [idc: %s]", idc)
		err = fmt.Errorf("param idc is illegal")
		return
	}

	// add all idc
	if allIDC {
		list := make([]string, 0)
		list, err = models.CloseAllIdcOneDoubleEnds(toService)
		if err != nil {
			golog.Warn("[open double ends] [add all idc double ends error] [err: %v]", err)
			return
		}
		data := simplejson.New()
		data.Set("idc", list)

		body := p.CreateBody(data)
		p.Echo(200, body)
		return
	}

	// add one idc
	err = models.CloseOneIdcOneDoubleEnds(toService, idc)
	if err != nil {
		golog.Warn("[open double ends] [add one idc double ends error] [idc: %s] [err: %v]", idc, err)
		return
	}

	data := simplejson.New()
	data.Set("idc", idc)

	body := p.CreateBody(data)
	p.Echo(200, body)

}
