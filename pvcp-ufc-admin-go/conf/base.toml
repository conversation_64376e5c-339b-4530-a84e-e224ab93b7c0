[server]
HttpPort = "8332"

# max number of CPUs to use
MaxCpuNum = 5


# mysql settings
[mysql]
server = "10.169.40.235:6006"
user = "ufc_w"
password = "pKidtgerw3Dtt_8T"
dbname = "ufc"
servicetable = "service"
streamtable = "stream"
ufcmetainfotable = "ufc_meta_info"


[log]
LogFile = "../logs/ufc-admin.log"
LogLevel = 8
# 5:ISIS
# 6:NOTICE
# 7:INFO
# 8:DEBUG


[pid]
filePath = "../logs/ufc-admin.pid"


[query]
column = [
  "service_name",
  "product"
]


[idc]
names = [
	"beijing",
	"yangquan",
	"qingdao",
	"shanghai",
	"xian"
]
base = "beijing"
physicalIdcs = [
	"bjyz",
	"st",
	"st01",
	"tc",
	"cq02",
	"bjhw",
	"cq01",
	"cp01",
	"bddx",
	"bddwd",
	"yq01",
	"yq012",
	"yq013",
	"yq01i",
	"njjs",
	"nj02",
	"qdbh",
	"gzns",
	"gzhxy",
	"gzhl",
	"sh01",
	"xaky",
	"xakd",
	"xafj",
	"szth",
	"szwg",
	"bdjl"
]

[cache]
prefix = "p-3ufc"
cloudcacheufc = "pss-cloudcache-bj"

[alarm]
names = "huanghaifeng02;huangzhe04"
url = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d78e0bf0a64864d01a7360b191b69f8eb"


[noah]
url = "http://api.mt.noah.baidu.com:8557/monquery/getHistoryitemdata?"