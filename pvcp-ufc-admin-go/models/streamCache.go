package models

import (
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

type StreamCache struct {
	Service string
}

func (m *StreamCache) GetStreamCacheKey(idc string, fromService, toService, ip string) (string, string) {
	pre := GetPrefix(idc)
	var httpKey string
	var streamKey string
	if fromService == "" {
		httpKey = fmt.Sprintf("%s-http_ip-%s", pre, toService)
		streamKey = fmt.Sprintf("%s-stream_ip-%s", pre, ip)
	} else {
		httpKey = fmt.Sprintf("%s-http_ip-%s|%s", pre, fromService, toService)
		streamKey = fmt.Sprintf("%s-stream_ip-%s", pre, ip)
	}

	return httpKey, streamKey
}

func (m *StreamCache) GetStreamCacheName(fromService, toService, ip string) (string, string) {
	var httpName string
	var streamName string
	streamName = ip
	if fromService == "" {
		httpName = toService
	} else {
		httpName = fmt.Sprintf("%s|%s", fromService, toService)
	}

	return httpName, streamName
}

func (m *StreamCache) GetServiceCache(key string) map[string]interface{} {
	v := utils.GetCache(key)
	if v == "" {
		return nil
	}

	var js map[string]interface{}
	err := json.Unmarshal([]byte(v), &js)
	if err != nil {
		return nil
	}

	return js
}

func (m *StreamCache) IsAdded(key string) bool {
	config := m.GetServiceCache(key)
	if config == nil {
		return false
	}
	return true
}

// 将service->ip 还有 ip->service 映射数据写到redis中
func (m *StreamCache) AddStream(idc string, toServiceIndex, fromServiceIndex uint32, fromService string) bool {
	// 根据index 生成相应的ip
	ip := utils.IpGenerate(fromServiceIndex, toServiceIndex)
	// 获取相应的key
	httpKey, streamKey := m.GetStreamCacheKey(idc, fromService, m.Service, ip)

	isHTTPAdd := m.IsAdded(httpKey)
	isStreamAdd := m.IsAdded(streamKey)

	if isHTTPAdd || isStreamAdd {
		SendHi(idc, "add", "stream", m.Service, "service-has-exist")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [stream has exist]")
		return false
	}

	// 生成需要插入的value
	httpValue := make(map[string]interface{})
	streamValue := make(map[string]interface{})

	if fromService == "" {
		httpValue["content"] = ip
		httpValue["index"] = fromServiceIndex
		streamValue["content"] = m.Service
	} else {
		httpValue["content"] = ip
		streamValue["content"] = fromService + "|" + m.Service
	}

	httpValueStr, httpErr := json.Marshal(httpValue)
	streamValueStr, streamErr := json.Marshal(streamValue)
	if httpErr != nil || streamErr != nil {
		SendHi(idc, "add", "stream", m.Service, "get-json-fail")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [marshal json fail]")
		return false
	}

	prefix := GetPrefix(idc)
	lockKey := prefix + "-stream-add"

	defer utils.UnLock(lockKey)
	if !utils.Lock(lockKey) {
		return false
	}

	mtime := time.Now().Unix()
	httpName, streamName := m.GetStreamCacheName(fromService, m.Service, ip)
	ok := m.AddCache(idc, httpKey, streamKey, string(httpValueStr), string(streamValueStr), mtime, httpName, streamName)
	if !ok {
		SendHi(idc, "add", "stream", m.Service, "add-stream-to-redis-fail")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [add stream fail]")
		return false
	}

	// 如果写入的是链路的映射还需要更新to service 中index的值
	ok = m.updateServiceIndex(idc, m.Service, fromServiceIndex)
	if !ok {
		SendHi(idc, "update", "stream", m.Service, "update-service-index-fail")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [update service index fail]")
		return false
	}

	SetGlobalVersion(prefix)
	return true
}

func (m *StreamCache) updateServiceIndex(idc, toService string, index uint32) bool {
	pre := GetPrefix(idc)
	httpKey := fmt.Sprintf("%s-http_ip-%s", pre, toService)
	httpValue := utils.GetCache(httpKey)
	if httpValue == "" {
		return false
	}
	var js map[string]interface{}
	err := json.Unmarshal([]byte(httpValue), &js)
	if err != nil {
		golog.Warn("unmarshal httpvalue failed, error is %v", err)
		return false
	}

	js["index"] = index
	httpbyte, err := json.Marshal(js)
	if err != nil {
		golog.Warn("marshal httpvalue failed, error is %v", err)
		return false
	}
	httpValue = string(httpbyte)
	mtime := time.Now().Unix()
	httpMtimeKey := fmt.Sprintf("%s-mtime", httpKey)
	re := utils.MsetCache(httpKey, httpValue, httpMtimeKey, mtime)
	if !re {
		return false
	}

	return true
}

func (m *StreamCache) AddCache(idc string, httpKey, streamKey, httpValue, streamValue string, mtime int64, httpName, streamName string) bool {
	pre := GetPrefix(idc)

	httpCntKey := fmt.Sprintf("%s-http_ips-cnt", pre)
	httpCnt := utils.GetIntCache(httpCntKey)
	if httpCnt < 0 {
		return false
	}
	httpCnt++

	streamCntKey := fmt.Sprintf("%s-stream_ips-cnt", pre)
	streamCnt := utils.GetIntCache(streamCntKey)
	if streamCnt < 0 {
		return false
	}
	streamCnt++

	httpMtimeKey := fmt.Sprintf("%s-mtime", httpKey)
	streamMtimeKey := fmt.Sprintf("%s-mtime", streamKey)

	httpNameKey := fmt.Sprintf("%s-http_ips-%d", pre, httpCnt-1)
	streamNameKey := fmt.Sprintf("%s-stream_ips-%d", pre, streamCnt-1)

	re := utils.MsetCache(httpNameKey, httpName, streamNameKey, streamName, httpCntKey, httpCnt, streamCntKey,
		streamCnt, httpKey, httpValue, streamKey, streamValue, httpMtimeKey, mtime, streamMtimeKey, mtime)
	if !re {
		return false
	}

	return true
}
