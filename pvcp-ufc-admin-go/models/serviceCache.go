package models

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"sync"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

type ServiceCache struct {
	Service string

	Mutex  sync.Mutex
	Errors []error
}

func (m *ServiceCache) AppendErrorWithLock(err error) {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()

	m.Errors = append(m.Errors, err)
}

// 删除redis中的数据
func (m *ServiceCache) Delete(idc string) error {
	pre := GetPrefix(idc)
	serviceCntKey := fmt.Sprintf("%s-services-cnt", pre)
	serviceCnt := utils.GetIntCache(serviceCntKey)
	if serviceCnt < 0 {
		// 获取cnt失败，发送hi报警
		golog.Warn("[Idc = " + idc + "] [service = " + m.Service + "] [get services cnt failed, delete this service fail]")
		SendHi(idc, "delete", "service", m.Service, "get service cnt failed")
		return fmt.Errorf("get service cnt failed in redis, key is %s", serviceCntKey)
	}
	// 找到需要删除的service的index
	index := m.GetServiceIndex(idc, serviceCnt)
	if index < 0 {
		golog.Warn("[Idc = " + idc + "] [service = " + m.Service + "] [this service is not exit in redis]")
		SendHi(idc, "delete", "service", m.Service, "this service is not exit in redis")
		return fmt.Errorf("this service is not exit in redis, redis key is %s", serviceCntKey)
	}
	golog.Info("[Idc = %s] [service = %s] [delete index is %d]", idc, m.Service, index)

	// 最后一个service name
	lastServiceNameKey := fmt.Sprintf("%s-services-%d", pre, serviceCnt-1)
	lastServiceName := utils.GetCache(lastServiceNameKey)
	if lastServiceName == "" {
		// 获取失败
		golog.Warn("[Idc = " + idc + "] [service = " + m.Service + "] [get last service name failed]")
		SendHi(idc, "delete", "service", m.Service, "get last service name failed")
		return fmt.Errorf("get last service name failed in redis, key is %s", lastServiceNameKey)
	}

	// 将service cnt减1，最后一个service name 覆盖到index上
	// 用mset做成一个原子操作
	changeServiceNameKey := fmt.Sprintf("%s-services-%d", pre, index)
	re := utils.MsetCache(serviceCntKey, serviceCnt-1, changeServiceNameKey, lastServiceName)
	if !re {
		// 写入失败
		golog.Warn("[Idc = " + idc + "] [service = " + m.Service + "] [delete failed]")
		SendHi(idc, "delete", "service", m.Service, "delete failed")
		return errors.New("delete failed in redis")
	}

	// 把需要删除的service相关信息删掉
	delServiceInfoKey := fmt.Sprintf("%s-service-%s", pre, m.Service)
	delServiceMtimeKey := fmt.Sprintf("%s-service-%s-mtime", pre, m.Service)
	delServiceCntKey := fmt.Sprintf("%s-services-%d", pre, serviceCnt-1)

	var deleteSuccess = true
	// delete service info
	if re := utils.DelCache(delServiceInfoKey); re != 1 {
		deleteSuccess = false
		err := fmt.Errorf("del service info key failed, redis key is %s", delServiceInfoKey)
		SendHi(idc, "delete", "service", m.Service, err.Error())
		golog.Warn("[Idc = "+idc+"] [service = "+m.Service+"] [err = %w]", err)
		m.AppendErrorWithLock(err) // 这里失败了不能return，仍需要继续删后续的key, 下同
	}
	// delete service mtime
	if re := utils.DelCache(delServiceMtimeKey); re != 1 {
		deleteSuccess = false
		err := fmt.Errorf("del service mtime key failed, redis key is %s", delServiceMtimeKey)
		SendHi(idc, "delete", "service", m.Service, err.Error())
		golog.Warn("[Idc = "+idc+"] [service = "+m.Service+"] [err = %w]", err)
		m.AppendErrorWithLock(err)
	}
	// delete last service index
	if re := utils.DelCache(delServiceCntKey); re != 1 {
		deleteSuccess = false
		err := fmt.Errorf("del service cnt key failed, redis key is %s", delServiceCntKey)
		SendHi(idc, "delete", "service", m.Service, err.Error())
		golog.Warn("[Idc = "+idc+"] [service = "+m.Service+"] [err = %w]", err)
		m.AppendErrorWithLock(err)
	}

	if deleteSuccess {
		golog.Info("[Idc = " + idc + "] [service = " + m.Service + "] [delete service success]")
	} else {
		golog.Warn("[Idc = " + idc + "] [service = " + m.Service + "] [delete service failed]")
	}

	return nil
}

// 找到service的index
func (m *ServiceCache) GetServiceIndex(idc string, serviceCnt int64) int64 {
	for i := int64(0); i < serviceCnt; i++ {
		pre := GetPrefix(idc)
		key := fmt.Sprintf("%s-services-%d", pre, i)
		serviceNameTemp := utils.GetCache(key)
		// 某一个key获取失败打个日志，但是继续往下走
		if serviceNameTemp == "" {
			golog.Warn("[Idc = " + idc + "] [get " + key + " fail]")
			continue
		}

		if serviceNameTemp == m.Service {
			return i
		}
	}
	return -1
}

func (m *ServiceCache) Add(idc string, config map[string]interface{}, idcEnable bool) error {
	key := m.GetServiceCacheKey(idc, m.Service)
	isAdd := m.IsAdded(key)
	if isAdd {
		SendHi(idc, "add", "service", m.Service, "service-has-exist")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [service has exist]")
		return errors.New("[Idc = " + idc + "] [add " + m.Service + " fail] [service has exist]")
	}

	bnsList := m.GetBnsList(config)
	if len(bnsList) < 1 {
		SendHi(idc, "add", "service", m.Service, "bnslist<1")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [bnslist len = 0]")
		return errors.New("[Idc = " + idc + "] [add " + m.Service + " fail] [bnslist len = 0]")
	}

	ok := m.CheckBnsList(idc, bnsList, idcEnable, config)
	if !ok {
		SendHi(idc, "add", "service", m.Service, "check-bnslist-fail")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [check BnsList fail]")
		return errors.New("[Idc = " + idc + "] [add " + m.Service + " fail] [check BnsList fail]")
	}

	prefix := GetPrefix(idc)
	lockKey := prefix + "-service-add"

	defer utils.UnLock(lockKey)
	if !utils.Lock(lockKey) {
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [lock " + lockKey + " fail]")
		return errors.New("[Idc = " + idc + "] [add " + m.Service + " fail] [lock " + lockKey + " fail]")
	}

	version, ok := config["mtime"].(int64)
	if !ok {
		SendHi(idc, "add", "service", m.Service, "get-config-mtime-fail")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [get version fail]")
		return errors.New("[Idc = " + idc + "] [add " + m.Service + " fail] [get version fail]")
	}

	js, err := json.Marshal(config)
	if err != nil {
		SendHi(idc, "add", "service", m.Service, "get-json-fail")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [marshal json fail]")
		return errors.New("[Idc = " + idc + "] [add " + m.Service + " fail] [marshal json fail]")
	}

	golog.Info("[Idc = " + idc + "] [Method = Set_Add_Cache] [Config = " + string(js) + "]")

	err = m.AddCache(idc, config, version)
	if err != nil {
		SendHi(idc, "add", "service", m.Service, "add-service-to-redis-fail")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [add service fail]")
		m.AppendErrorWithLock(err)
		return errors.New("[Idc = " + idc + "] [add " + m.Service + " fail] [add service fail]")
	}

	if ok := SetGlobalVersion(prefix); !ok {
		SendHi(idc, "add", "service", m.Service, "set global version fail")
		golog.Warn("[Idc = " + idc + "] [add " + m.Service + " fail] [prefix: " + prefix + "] [set global version failed]")
		return fmt.Errorf("set redis global version fail, prefix: %s", prefix)
	}

	return nil
}

func (m *ServiceCache) Update(idc string, args map[string]interface{}, idcEnable bool) error {
	key := m.GetServiceCacheKey(idc, m.Service)
	isAdd := m.IsAdded(key)
	if !isAdd {
		SendHi(idc, "update", "service", m.Service, "service-not-exist")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [service not exist]")
		return errors.New("[Idc = " + idc + "] [update " + m.Service + " fail] [service not exist]")
	}

	bnsList := m.GetBnsList(args)
	if len(bnsList) < 1 {
		SendHi(idc, "update", "service", m.Service, "bnslist<1")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [bnslist len = 0]")
		return errors.New("[Idc = " + idc + "] [update " + m.Service + " fail] [bnslist len = 0]")
	}

	ok := m.CheckBnsList(idc, bnsList, idcEnable, args)
	if !ok {
		SendHi(idc, "update", "service", m.Service, "check-bnslist-fail")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [check BnsList fail]")
		return errors.New("[Idc = " + idc + "] [update " + m.Service + " fail] [check BnsList fail]")
	}

	prefix := GetPrefix(idc)
	lockKey := prefix + "-service-update"
	defer utils.UnLock(lockKey)

	if !utils.Lock(lockKey) {
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [lock " + lockKey + " fail]")
		return errors.New("[Idc = " + idc + "] [update " + m.Service + " fail] [lock " + lockKey + " fail]")
	}

	version, ok := args["mtime"].(int64)
	if !ok {
		SendHi(idc, "update", "service", m.Service, "get-config-mtime-fail")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [get version fail]")
		return errors.New("[Idc = " + idc + "] [update " + m.Service + " fail] [get version fail]")
	}

	args["mtime"] = version
	content, err := json.Marshal(args)
	if err != nil {
		SendHi(idc, "update", "service", m.Service, "get-json-fail")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [marshal json fail]")
		return errors.New("[Idc = " + idc + "] [update " + m.Service + " fail] [marshal json fail]")
	}

	golog.Info("[Idc = " + idc + "] [Method = Set_Update_Cache] [Config = " + string(content) + "]")
	ok = m.SetConfigCache(idc, args)
	if !ok {
		SendHi(idc, "update", "service", m.Service, "update-service-to-redis-fail")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [SetConfigCache fail]")
		return errors.New("set config cache failed")
	}

	ok = m.SetServiceVersionCache(idc, version)
	if !ok {
		SendHi(idc, "update", "service", m.Service, "update-mtime-to-redis-fail")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [SetServiceVersionCache json fail]")
		return errors.New("set service version cache failed")
	}

	if ok := SetGlobalVersion(prefix); !ok {
		SendHi(idc, "add", "service", m.Service, "set global version fail")
		golog.Warn("[Idc = " + idc + "] [update " + m.Service + " fail] [prefix: " + prefix + "] [set global version failed]")
		return fmt.Errorf("set redis global version fail, prefix: %s", prefix)
	}

	return nil
}

func (m *ServiceCache) IsAdded(key string) bool {
	config := m.GetServiceCache(key)
	if config == nil {
		return false
	}

	return true
}

func (m *ServiceCache) GetServiceCacheKey(idc string, serviceName string) string {
	pre := GetPrefix(idc)
	key := fmt.Sprintf("%s-service-%s", pre, serviceName)
	return key
}

func (m *ServiceCache) GetServiceCache(key string) map[string]interface{} {
	v := utils.GetCache(key)
	if v == "" {
		return nil
	}

	var js map[string]interface{}
	err := json.Unmarshal([]byte(v), &js)
	if err != nil {
		return nil
	}

	return js
}

func (m *ServiceCache) CheckBnsList(idc string, items []string, idcMapEnable bool, config map[string]interface{}) bool {
	isFakeBns := m.isFakeBns(config)
	for _, bns := range items {
		b := &BnsCache{Idc: idc, Bns: bns, IsFakeBns: isFakeBns}
		ok := b.isAdded("bns")
		if ok {
			res := b.Update(idcMapEnable, config)
			if !res {
				SendHi(idc, "update", "bns", bns, "update-bns-fail")
				errMsg := "[Idc = " + idc + "] [update bns fail] [bns = " + bns + "]"
				golog.Warn(errMsg)
				b.AppendErrorWithLockInBnsCache(errors.New(errMsg))
				return false
			}
		} else {
			res := b.Add(idcMapEnable, config)
			if !res {
				SendHi(idc, "add", "bns", bns, "add-bns-fail")
				errMsg := ("[Idc = " + idc + "] [add bns fail] [bns = " + bns + "]")
				golog.Warn(errMsg)
				b.AppendErrorWithLockInBnsCache(errors.New(errMsg))
				return false
			}
		}

		for _, bnsErr := range b.Errors {
			m.AppendErrorWithLock(bnsErr)
		}
	}

	return true
}

func (m *ServiceCache) SetServiceVersionCache(idc string, version int64) bool {
	pre := GetPrefix(idc)
	key := fmt.Sprintf("%s-service-%s-mtime", pre, m.Service)

	re := utils.SetCache(key, version)
	if !re {
		return false
	}

	return true
}

func (m *ServiceCache) SetNameCache(idc string, version int64) bool {
	if version < 1 {
		return false
	}

	pre := GetPrefix(idc)
	key := fmt.Sprintf("%s-services-%d", pre, version-1)

	re := utils.SetCache(key, m.Service)
	if !re {
		return false
	}

	return true
}

func (m *ServiceCache) GetNameVersion(idc string) int64 {
	pre := GetPrefix(idc)
	key := fmt.Sprintf("%s-services-cnt", pre)

	v := utils.SetIncr(key)
	return v
}

func (m *ServiceCache) GetBnsList(config map[string]interface{}) []string {
	var bnsList []string

	bnsMaps, ok := config["bns_list"].(map[string]interface{})
	if ok {
		for bns := range bnsMaps {
			bnsList = append(bnsList, bns)
		}
	}

	smallFlowBns, ok := m.getSmallFlowBns(config)
	if ok {
		bnsList = append(bnsList, smallFlowBns...)
	}

	advancedRouteBns, ok := m.getAdvancedRouteBns(config)
	if ok {
		bnsList = append(bnsList, advancedRouteBns...)
	}

	return bnsList
}

func (m *ServiceCache) getSmallFlowBns(config map[string]interface{}) ([]string, bool) {
	smallArr, ok := config["small_flow"].([]interface{})
	if !ok {
		return nil, false
	}

	if len(smallArr) < 1 {
		return nil, false
	}

	smallFlow, ok := smallArr[0].(map[string]interface{})
	if !ok {
		return nil, false
	}

	smallBns, ok := smallFlow["bns_list"].(map[string]interface{})
	if !ok {
		return nil, false
	}

	var smallFlowBns []string
	for bns := range smallBns {
		smallFlowBns = append(smallFlowBns, bns)
	}

	if len(smallFlowBns) == 0 {
		return nil, false
	}

	return smallFlowBns, true
}

func (m *ServiceCache) getAdvancedRouteBns(config map[string]interface{}) ([]string, bool) {
	// 高级路由配置更新
	advancdRoute, ok := config["advanced_route"].(map[string]map[string]interface{})
	if !ok {
		return nil, false
	}

	routeApps, ok := advancdRoute["route_app"]
	if !ok {
		golog.Error("route_app is not in advanced route config")
		return nil, false
	}

	var advancdRouteBns []string
	for _, val := range routeApps {
		routeAppBns, ok := val.(map[string]interface{})
		if !ok {
			golog.Error("routeApps val type check failed")
			continue
		}
		for bns := range routeAppBns {
			golog.Info("add route app bns success, bns name is %s", bns)
			advancdRouteBns = append(advancdRouteBns, bns)
		}
	}

	if len(advancdRouteBns) == 0 {
		return nil, false
	}

	return advancdRouteBns, true
}

func (m *ServiceCache) SetConfigCache(idc string, config map[string]interface{}) bool {
	if config == nil {
		return false
	}

	pre := GetPrefix(idc)
	key := fmt.Sprintf("%s-service-%s", pre, m.Service)

	str, err := json.Marshal(config)
	if err != nil {
		return false
	}

	content := string(str)
	re := utils.SetCache(key, content)
	if !re {
		return false
	}

	return true
}

func (m *ServiceCache) AddCache(idc string, config map[string]interface{}, version int64) error {
	pre := GetPrefix(idc)

	serviceCntKey := fmt.Sprintf("%s-services-cnt", pre)
	serviceCnt := utils.GetIntCache(serviceCntKey)
	if serviceCnt < 0 {
		return fmt.Errorf("service cache cnt %d is less than 0", serviceCnt)
	}
	serviceCnt++

	serviceNameKey := fmt.Sprintf("%s-services-%d", pre, serviceCnt-1)

	if config == nil {
		return fmt.Errorf("add cache failed, because config is nil")
	}

	serviceConfigKey := fmt.Sprintf("%s-service-%s", pre, m.Service)
	str, err := json.Marshal(config)
	if err != nil {
		return err
	}
	serviceConfig := string(str)

	serviceMtimeKey := fmt.Sprintf("%s-service-%s-mtime", pre, m.Service)

	// add ($pre-services-$index, $serviceName)
	err = AddUfcMetaInfo(serviceNameKey, m.Service)
	if err != nil {
		//写mysql失败，发hi报警
		SendHi(idc, "add", "service", m.Service, "add ufc_meta_info failed")
		err := fmt.Errorf("add ufc_meta_info failed, key is %s, value is %s, err msg: %w ", serviceNameKey, m.Service, err) // 由于多处
		m.AppendErrorWithLock(err)
	}

	// 如果上条 add 成功， 下条失败的话，这个 add 接口jiu

	// update ("pre-service-cnt", $cnt) 到数据库
	err = UpdateUfcMetaInfo(serviceCntKey, strconv.Itoa(int(serviceCnt)))
	if err != nil {
		//写mysql失败，发hi报警
		SendHi(idc, "update", "service", m.Service, "update ufc_meta_info failed")
		outErr := fmt.Errorf("add ufc_meta_info failed, key is %s, value is %d, err is %w", serviceCntKey, serviceCnt, err)
		m.AppendErrorWithLock(outErr)
	}

	re := utils.MsetCache(serviceCntKey, serviceCnt, serviceNameKey, m.Service, serviceConfigKey, serviceConfig, serviceMtimeKey, version)
	if !re {
		sendHiMsg := fmt.Sprintf("[mset service failed][%s:%d][%s:%s][%s:%s][%s:%d]",
			serviceCntKey, serviceCnt, serviceNameKey, m.Service, serviceConfigKey, serviceConfig, serviceMtimeKey, version)
		SendHi(idc, "add", "service", m.Service, sendHiMsg)
		err := fmt.Errorf("add service failed, add mset cache failed")
		return err
	}

	return nil
}
func (m *ServiceCache) isFakeBns(config map[string]any) bool {
	val, ok := config["fake_bns_exist"]
	if !ok {
		return false
	}
	// amis上传递的是 bool是true, 存储的是 int, 恢复到 ufc-admin 有可能是各种类型
	switch val.(type) {
	case float64:
		return val == 1
	case int:
		return val == 1
	case string:
		return val == "1" // 该场景命中
	case bool:
		valBool := val.(bool)
		return valBool
	default:
		svcName := config["service_name"]
		golog.Error("service fake_bns_exist type check failed, svc name is %s, type is %v, value is %v", svcName, reflect.TypeOf(val), val)
		return false
	}
}
