package models

import (
	"fmt"
	"io/ioutil"
	"os"
	"strings"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/toml"
)

type baseConf struct {
	Server ServerConf
	Mysql  MysqlConf
	Log    LogConf
	Pid    PidConf
	Idc    IdcConf
	Cache  CacheConf
	Alarm  AlarmConf
	Noah   NoahConf
}

type MysqlConf struct {
	Server           string
	User             string
	Password         string
	Dbname           string
	ServiceTable     string
	UfcMetaInfoTable string
	StreamTable      string
}

type ServerConf struct {
	HttpPort  string
	MaxCpuNum int
}

type LogConf struct {
	LogFile  string
	LogLevel int32
}

type CacheConf struct {
	Prefix        string
	CloudCacheUfc string
}

type PidConf struct {
	FilePath string
}

type IdcConf struct {
	Names        []string
	Base         string
	PhysicalIdcs []string
}

type AlarmConf struct {
	Names string
	Url   string
}

type NoahConf struct {
	Url string
}

var (
	BaseConf = baseConf{}

	// conf map/slice
	ReqBodyMysqlParamMap  map[string]string
	ReqBodyMustParamMap   map[string]string
	ReqBodyJSONParamMap   map[string]string
	ReqBodyStringParamMap map[string]string
	ReqBodyIntParamMap    map[string]string

	// 注册fakebns, 不支持 idcmap
	ReqBodyIdcMapParamMap map[string]string

	CacheColumnMap     map[string]string
	CacheJSONColumnMap map[string]string
)

func InitMapConf() {
	// body字段 vs 数据库字段名
	ReqBodyMysqlParamMap = map[string]string{
		"service_name":           "service_name",
		"product":                "product",
		"bns_list":               "bns_list",
		"connect_timeout":        "connect_timeout",
		"read_timeout":           "read_timeout",
		"person":                 "person",
		"bp":                     "bp",
		"send_timeout":           "send_timeout",
		"forbid_timeout":         "forbid_timeout",
		"max_forbid_percent":     "max_forbid_percent",
		"request_reset_interval": "request_reset_interval",
		"max_fail":               "max_fail",
		"limit_qps_per_backend":  "limit_qps_per_backend",
		"limit_qps_all":          "limit_qps_all",
		"limit_qps_running":      "limit_qps_running",
		"qps_drop_rate":          "qps_drop_rate",
		"max_global_running_cnt": "max_global_running_cnt",
		"backends_max_tries":     "backends_max_tries",
		"hash_pool_size":         "hash_pool_size",
		"worker_timeout":         "worker_timeout",
		"protocol":               "protocol",
		"options":                "options",
		"backup":                 "backup",
		"illegal_headers":        "illegal_headers",
		"black_list":             "black_list",
		"idc_map":                "idc_map",
		"small_flow_protocol":    "small_flow_protocol",
		"small_flow_options":     "small_flow_options",
		"small_flow_bns_list":    "small_flow_bns_list",
		"small_flow_inner":       "small_flow_inner",
		"small_flow_hash_range":  "small_flow_hash_range",
		"small_flow_small_list":  "small_flow_small_list",
		"small_flow_idc_map":     "small_flow_idc_map",
		"description":            "description",
		"mtime":                  "mtime",
		"breaker_policy":         "breaker_policy",
		"advanced_route_rule":    "advanced_route_rule",
		"advanced_route_app":     "advanced_route_app",
		"fake_bns_exist":         "fake_bns_exist",
		"fake_bns_list_data":     "fake_bns_list_data",
	}

	// body中必须有的字段
	ReqBodyMustParamMap = map[string]string{
		"service_name":    "service_name",
		"product":         "product",
		"bns_list":        "bns_list",
		"connect_timeout": "connect_timeout",
		"read_timeout":    "read_timeout",
		"person":          "person",
	}

	ReqBodyJSONParamMap = map[string]string{
		"bns_list":              "bns_list",
		"options":               "options",
		"backup":                "backup",
		"illegal_headers":       "illegal_headers",
		"black_list":            "black_list",
		"idc_map":               "idc_map",
		"small_flow_bns_list":   "small_flow_bns_list",
		"small_flow_hash_range": "small_flow_hash_range",
		"small_flow_protocol":   "small_flow_protocol",
		"small_flow_small_list": "small_flow_small_list",
		"small_flow_idc_map":    "small_flow_idc_map",
		"breaker_policy":        "breaker_policy",
		"advanced_route_rule":   "advanced_route_rule",
		"advanced_route_app":    "advanced_route_app",
		"fake_bns_list_data":    "fake_bns_list_data",
	}

	ReqBodyStringParamMap = map[string]string{
		"service_name":          "service_name",
		"product":               "product",
		"bns_list":              "bns_list",
		"person":                "person",
		"protocol":              "protocol",
		"options":               "options",
		"backup":                "backup",
		"illegal_headers":       "illegal_headers",
		"black_list":            "black_list",
		"idc_map":               "idc_map",
		"small_flow_protocol":   "small_flow_protocol",
		"small_flow_options":    "small_flow_options",
		"small_flow_bns_list":   "small_flow_bns_list",
		"small_flow_inner":      "small_flow_inner",
		"small_flow_hash_range": "small_flow_hash_range",
		"small_flow_small_list": "small_flow_small_list",
		"small_flow_idc_map":    "small_flow_idc_map",
		"description":           "description",
		"breaker_policy":        "breaker_policy",
	}

	ReqBodyIdcMapParamMap = map[string]string{
		"idc_map":            "idc_map",
		"small_flow_idc_map": "small_flow_idc_map",
	}

	ReqBodyIntParamMap = map[string]string{
		"bp":                     "bp",
		"max_fail":               "max_fail",
		"read_timeout":           "read_timeout",
		"send_timeout":           "send_timeout",
		"qps_drop_rate":          "qps_drop_rate",
		"worker_timeout":         "worker_timeout",
		"forbid_timeout":         "forbid_timeout",
		"hash_pool_size":         "hash_pool_size",
		"connect_timeout":        "connect_timeout",
		"limit_qps_running":      "limit_qps_running",
		"max_forbid_percent":     "max_forbid_percent",
		"limit_qps_per_backend":  "limit_qps_per_backend",
		"max_global_running_cnt": "max_global_running_cnt",
		"request_reset_interval": "request_reset_interval",
	}

	CacheColumnMap = map[string]string{
		"service_name":           "service_name",
		"product":                "product",
		"connect_timeout":        "connect_timeout",
		"read_timeout":           "read_timeout",
		"backends_max_tries":     "backends_max_tries",
		"bp":                     "bp",
		"send_timeout":           "send_timeout",
		"forbid_timeout":         "forbid_timeout",
		"max_forbid_percent":     "max_forbid_percent",
		"request_reset_interval": "request_reset_interval",
		"max_fail":               "max_fail",
		"limit_qps_per_backend":  "limit_qps_per_backend",
		"limit_qps_all":          "limit_qps_all",
		"limit_qps_running":      "limit_qps_running",
		"qps_drop_rate":          "qps_drop_rate",
		"max_global_running_cnt": "max_global_running_cnt",
		"worker_timeout":         "worker_timeout",
		"mtime":                  "mtime",
		"idc_map":                "idc_map",
		"breaker_policy":         "breaker_policy",
		"fake_bns_exist":         "fake_bns_exist",
	}

	CacheJSONColumnMap = map[string]string{
		"illegal_headers":    "illegal_headers",
		"black_list":         "black_list",
		"bns_list":           "bns_list",
		"idc_map":            "idc_map",
		"breaker_policy":     "breaker_policy",
		"fake_bns_list_data": "fake_bns_list_data",
	}
}

func ParseBaseConf() error {
	return parseConf("../conf/base.toml", &BaseConf)
}

func parseConf(path string, result interface{}) error {
	f, err := os.Open(path)
	if err != nil {
		return err
	}

	defer f.Close()

	buf, err := ioutil.ReadAll(f)
	if err != nil {
		return err
	}

	if err := toml.Unmarshal(buf, result); err != nil {
		return err
	}

	return nil
}

func CheckConfigValid() error {
	svcName := BaseConf.Mysql.ServiceTable
	ufcMetaInfoName := BaseConf.Mysql.UfcMetaInfoTable
	streamTableName := BaseConf.Mysql.StreamTable
	cloudCacheUfcName := BaseConf.Cache.CloudCacheUfc
	cloudCachePrefix := BaseConf.Cache.Prefix
	// 线上环境配置
	if svcName == "service" && ufcMetaInfoName == "ufc_meta_info" &&
		streamTableName == "stream" && cloudCachePrefix == "p-3ufc" &&
		!strings.Contains(cloudCacheUfcName, "sandbox") {
		return nil
	}

	// 线下环境配置
	if svcName == "sandbox_service" && ufcMetaInfoName == "sandbox_ufc_meta_info" &&
		streamTableName == "sandbox_stream" && cloudCachePrefix == "p-3ufc-sandbox" &&
		strings.Contains(cloudCacheUfcName, "sandbox") {
		return nil
	}
	golog.Error("[table name is not valid] [service table: %s] [ufc meta info table: %s][stream table: %s] [redis prefix: %s]",
		svcName, ufcMetaInfoName, streamTableName, cloudCachePrefix)
	return fmt.Errorf("[table name is not valid][service table:%s][ufc meta info table: %s][stream table: %s][redis prefix: %s]",
		svcName, ufcMetaInfoName, streamTableName, cloudCachePrefix)
}
