package models

import (
	"fmt"
	"math/rand"
	"sync"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/bns"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/protobuf/proto"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/simplejson"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

func GetSmallFlowIps(serviceName string) []string {
	res := make([]string, 0)
	prefix := BaseConf.Cache.Prefix
	baseIdc := BaseConf.Idc.Base
	key := fmt.Sprintf("%s-%s-service-%s-ips", prefix, baseIdc, serviceName)
	redisData, err := utils.Hgetall(key)
	if err != nil {
		return res
	}
	for k := range redisData {
		res = append(res, k)
	}
	return res
}

func ForIdcAdd(serviceName string, ips string) error {
	idcs := BaseConf.Idc.Names

	var mutex sync.Mutex
	var errors []error

	var wg sync.WaitGroup
	wg.Add(len(idcs))
	for _, idc := range idcs {
		idc := idc
		go func() {
			defer wg.Done()

			err := addOneIdc(serviceName, idc, ips) //
			if err != nil {
				mutex.Lock()
				errors = append(errors, err)
				mutex.Unlock()
			}
		}()
	}
	wg.Wait()

	if len(errors) != 0 {
		return utils.MergeError(errors...)
	}
	return nil
}

func ForIdcDelete(serviceName string) error {
	idcs := BaseConf.Idc.Names

	var mutex sync.Mutex
	var errors []error

	var wg sync.WaitGroup
	wg.Add(len(idcs))
	for _, idc := range idcs {
		idc := idc
		go func() {
			defer wg.Done()
			if err := deleteOneIdc(serviceName, idc); err != nil {
				mutex.Lock()
				errors = append(errors, err)
				mutex.Unlock()
			}
		}()
	}
	wg.Wait()

	if len(errors) != 0 {
		return utils.MergeError(errors...)
	}
	return nil
}

func deleteOneIdc(serviceName, idc string) error {
	prefix := BaseConf.Cache.Prefix
	key := fmt.Sprintf("%s-%s-service-%s-ips", prefix, idc, serviceName)
	re := utils.DelCache(key)
	if re != 1 {
		return fmt.Errorf(`delete smallflow pool of idc  %s failed`, idc)
	}

	return nil
}

func addOneIdc(serviceName, idc string, ips string) error {
	prefix := BaseConf.Cache.Prefix
	key := fmt.Sprintf("%s-%s-service-%s-ips", prefix, idc, serviceName)
	ok := utils.Hset(key, ips, "in")
	if !ok {
		SendHi(idc, "update", "smallflowips", key, "add ips to smallflow failed")
		golog.Error("[add ips to smallflow pool failed] [rediskey: %s] [ips: %s]", key, ips)
		return fmt.Errorf("add ips to smallflow pool failed, key: %v, ips: %s", key, ips)
	}
	return nil
}

// 根据to_service获取上游机器
func GetUpstreamIps(toService string) string {
	key := "p-5ufc-admin-smallflow-" + toService
	fromServices, err := utils.Smembers(key)
	if err != nil {
		golog.Warn("to_service %v, get from services failed, error is %v", toService, err)
		return ""
	}

	if len(fromServices) == 0 {
		golog.Warn("to_service %v, from services number is zero", toService)
		return ""
	}

	for _, fromService := range fromServices {
		ip := getServiceIP(fromService)
		if ip != "" {
			return ip
		}
	}
	return ""
}

func getServiceIP(serviceName string) string {
	prefix := BaseConf.Cache.Prefix
	baseIdc := BaseConf.Idc.Base
	key := fmt.Sprintf("%s-%s-service-%s", prefix, baseIdc, serviceName)
	serviceConfigStr := utils.GetCache(key)
	if serviceConfigStr == "" {
		return ""
	}
	serviceConfig, err := simplejson.NewJson([]byte(serviceConfigStr))
	if err != nil {
		golog.Warn("service %v, config %v is not json format", serviceName, serviceConfig)
		return ""
	}

	bnsList, err := serviceConfig.Get("bns_list").Map()
	if err != nil {
		golog.Warn("service %v, config %v format error", serviceName, serviceConfig)
		return ""
	}
	for bns := range bnsList {
		ip := getIPFromBns(bns)
		if ip != "" {
			return ip
		}
	}
	return ""
}

// 从bns中随机获取一个ip
func getIPFromBns(bnsName string) string {
	bnsClient := bns.New()
	var reply bns.LocalNamingResponse

	err := bnsClient.Call(&bns.LocalNamingRequest{ServiceName: proto.String(bnsName)}, &reply)
	if err != nil {
		golog.Warn("bns:%s, error:%s, get instance info error", bnsName, err.Error())
		return ""
	}

	bnsLen := len(reply.InstanceInfo)
	index := rand.Intn(bnsLen)
	ipInt := reply.InstanceInfo[index].HostIp
	ipStr := fmt.Sprintf("%d.%d.%d.%d", byte(*ipInt>>24), byte(*ipInt>>16), byte(*ipInt>>8), byte(*ipInt))
	return ipStr
}
