/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: CodemetricController层全局拦截过滤器
 */
package action

// codemetric内接口core执行前准备数据，一般用于配置（dto service 未初始化）
func (t *CodemetricController) Prepare() error {
	t.SignVerifyOnOff = false	// 接口参数验签开关，默认是关闭。
	t.Logger.Info("[pos:CodemetricController.Prepare] [func:%s]", t.Func<PERSON>ame)
	return nil
}

// codemetric内接口core执行前 dto service 已初始化，参数校验完成
func (t *CodemetricController) Before() error {
	t.Logger.Info("[pos:CodemetricController.Before] [func:%s]", t.FuncName)
	return nil
}

// codemetric内接口core执行后
func (t *CodemetricController) After() {
	t.Logger.Info("[pos:CodemetricController.After] [func:%s]", t.<PERSON>)
}
