/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: WfmetricController单元测试
 */
package action

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
)

// api 测试总入口
func TestApi(t *testing.T) {
	// 初始化配置文件,如果在根目录启动则不用设置
	config_mock.SetConfRootPath("../../conf")
	// 框架执行初始化逻辑，加载框架配置文件
	err := easy.Init("")
	// 断言初始化是否成功
	assert.Nil(t, err)
	// 1.初始化要测试controller
	c := &WfmetricController{}
	// 2.构建测试数据
	tests := []struct {
		name	string
		args	*mock.ReqArgs
	}{
		{
			name:	"test api:/wfmetric/add",	// 测试名称标识
			args: &mock.ReqArgs{
				Exec:		c.Add,	// 测试的接口入口函数
				Method:		http.MethodPost,
				RequestURI:	"/wfmetric/add",	// 测试接口地址
				Header: http.Header{
					"X-TANGRAM-LOG-ID":	[]string{"1235671231551"},	// logid染色
					"X-TANGRAM-CALL-ID":	[]string{"0.1.1"},		// callid染色
					"Content-Type":		[]string{"application/x-www-form-urlencoded"},
				},
				Data:	mock.BuildData(func(p *mock.MockParams) {}),	// request入参
			},
		},
	}
	// 执行API单测
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// 在action层有多个接口批量进行单元测试，在执行单元测试之前，需要将结构体初始化一次，
			// 避免前后上下文覆盖，导致实际上只有一个接口执行的问题
			MockRegistry(t)
			c.Mock(test.args, func(bytes []byte, e error) {
				assert.Nil(t, e)
				// 获取response 内容 bytes 编写测试逻辑
				// 打印结果
				tangram_logger.Info("dump response:\n%s", bytes)
			})
		})
	}
}

// 注册驱动
func MockRegistry(t *testing.T) {
	var err error
	// 注册直连sql驱动
	err = mock.MockSqlDirectRegistry("mysql_cloud_zhongzhenyan",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "zhongzhenyan"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_tangram_dump",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "tangram_dump"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_ai_netdisk",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "ai_netdisk"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_ai_trans",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "ai_trans"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_anti_scene",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "anti_scene"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_global_pass_session",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "global_pass_session"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_aifairy",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "aifairy"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_aiconvert",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "aiconvert"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_deepcopy",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "deepcopy"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_global_exp",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "global_exp"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_anti_online",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "anti_online"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_CL_Notice",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "CL_Notice"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_ant_cdn",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "ant_cdn"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_anti_risk_center",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "anti_risk_center"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_aipet",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "aipet"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_aipic",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "aipic"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_baidu_dba",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "baidu_dba"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_gpttest",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "gpttest"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_global_ad",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "global_ad"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_corpus",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "corpus"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_easydb",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "easydb"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_activity_ljs_test",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "activity_ljs_test"})
	assert.Nil(t, err)
	// 注册直连redis驱动
	err = mock.MockRedisDirectRegistry("cache-bj", "************:8841")
	assert.Nil(t, err)
	// 注册直连http驱动, 测试的host链接由业务指定
	// 示例:err = mock.MockHttpDirectRegistry("confName","http://localhost:8080")
	err = mock.MockHttpDirectRegistry("naclocal", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("nacmaster", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("easycode", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("feStatistics", "")
	assert.Nil(t, err)
}
