/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: wfmetric模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/wfmetric"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/wfmetric"
)

// Wfmetric 路由接口对应的Controller,包括入参Dto以及service
type WfmetricController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	WfmetricService *service.WfmetricService
	AddDto          *dto.AddReqDto
	QueryDto        *dto.QueryReqDto
}

// add接口
func (t *WfmetricController) Add() {
	easy.Exec(t)
}

// 查询度量信息接口
func (t *WfmetricController) Query() {
	easy.Exec(t)
}
