/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: sqldata模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/sqldata"
)

// Sqldata 路由接口对应的Controller,包括入参Dto以及service
type SqldataController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	UploadDto    *dto.UploadReqDto
	DiffviewDto  *dto.DiffviewReqDto
	ReviewDto    *dto.ReviewReqDto
	StatDto      *dto.StatReqDto
	IssueshowDto *dto.IssueshowReqDto
}

// 上传 sqldata 数据
func (t *SqldataController) Upload() {
	easy.Exec(t)
}

// 查看diff内容
func (t *SqldataController) Diffview() {
	easy.Exec(t)
}

// review 接口
func (t *SqldataController) Review() {
	easy.Exec(t)
}

// 数据统计
func (t *SqldataController) Stat() {
	easy.Exec(t)
}

// 问题SQL展示
func (t *SqldataController) Issueshow() {
	easy.Exec(t)
}
