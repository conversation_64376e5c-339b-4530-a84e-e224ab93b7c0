/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: easyrule模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/easyrule"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/easyrule"
)

// Easyrule 路由接口对应的Controller,包括入参Dto以及service
type EasyruleController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	EasyruleService *service.EasyruleService
	AddDto          *dto.AddReqDto
	ListDto         *dto.ListReqDto
}

// add
func (t *EasyruleController) Add() {
	easy.Exec(t)
}

// list所有数据
func (t *EasyruleController) List() {
	easy.Exec(t)
}
