/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: metric模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/metric"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/metric"
)

// Metric 路由接口对应的Controller,包括入参Dto以及service
type MetricController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	MetricService *service.MetricService
	AddDto        *dto.AddReqDto
	IncreDto      *dto.IncreReqDto
}

// ee侧统计需求
func (t *MetricController) Add() {
	easy.Exec(t)
}

// list
func (t *MetricController) Incre() {
	easy.Exec(t)
}
