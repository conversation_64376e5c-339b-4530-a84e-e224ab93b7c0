#!/usr/bin/env bash

set -x

deployDir=$(pwd)

success_exit_code=0
starting_exit_code=1
error_exit_code=2

TANGRAM_HOME=$(printenv TANGRAM_HOME)
if [ $? -ne 0 ]; then
  TANGRAM_HOME="/home/<USER>/tangram"
  export TANGRAM_HOME=${TANGRAM_HOME}
fi

TANGRAM_CONTROL=$(printenv TANGRAM_CONTROL)
if [ $? -ne 0 ]; then
  TANGRAM_CONTROL=${TANGRAM_HOME}/CONTROL
fi

moduleName="EasyCodeRuleDataServer.so"

version() {
  version=$(md5sum ${deployDir}/bin/${moduleName} | awk '{print $1}')
  echo $version
  exit 0
}

port_check() {
  port=${1}
  nc -zv 127.0.0.1 ${port}
  if [ $? -ne 0 ]; then
    exit 2
  fi
}

status() {
  ${TANGRAM_CONTROL} status
  if [ $? -ne 0 ]; then
    exit ${error_exit_code}
  fi
  # tangram will auto check tcp port, you can do other here
  #  port_check ${port_main}

  echo ${success_exit_code}
}

update() {
  ${TANGRAM_CONTROL} pre_update
  ${TANGRAM_CONTROL} update
  ${TANGRAM_CONTROL} post_update
}

restart() {
  ${TANGRAM_CONTROL} pre_restart
  ${TANGRAM_CONTROL} restart
  ${TANGRAM_CONTROL} post_restart
}

force_restart() {
  ${TANGRAM_CONTROL} pre_force_restart
  ${TANGRAM_CONTROL} force_restart
  ${TANGRAM_CONTROL} post_force_restart
}

stop() {
  ${TANGRAM_CONTROL} pre_stop
  ${TANGRAM_CONTROL} stop
  ${TANGRAM_CONTROL} post_stop
}

start() {
  ${TANGRAM_CONTROL} pre_start
  ${TANGRAM_CONTROL} start
  ${TANGRAM_CONTROL} post_start
}

case "$1" in
version)
  version
  ;;
status)
  status
  ;;
restart)
  restart
  ;;
update)
  update
  ;;
stop)
  stop
  ;;
force_restart)
  force_restart
  ;;
start)
  start
  ;;
*)
  echo "Usage: $0 {start|stop|restart|update|force_restart|status|version}"
  ;;
esac
