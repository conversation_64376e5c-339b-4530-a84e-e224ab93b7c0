package baseutil

import (
	"bytes"
	"encoding/json"
	"net/http"
	"os"
	"strings"
)

func GetMachine() string {
	name := getMatrixID()
	if strings.TrimSpace(name) == "" {
		var err error
		name, err = os.Hostname()
		if err != nil {
			name = ""
		}
	}
	return name
}

func getMatrixID() string {
	return os.Getenv("MATRIX_INSTANCE_ID")
}

type RobotArg struct {
	Msg RobotMsg `json:"message"`
}

type RobotMsg struct {
	Body []IRobotBody `json:"body"`
}

type IRobotBody interface {
	robotBodyNode()
}

type RobotBodyText struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

func (r *RobotBodyText) robotBodyNode() {}

type RobotBodyAt struct {
	Type      string   `json:"type"`
	AtUserIDs []string `json:"atuserids"`
	AtAll     bool     `json:"atall"`
}

func (r *RobotBodyAt) robotBodyNode() {}

type RobotBodyMD struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

func (r *RobotBodyMD) robotBodyNode() {}

// TODO: LINK

type SendHiType = string

// 如流 Hi 报警文档： https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/2tsPs8CtSd/Bu7DDg4dpB/Q82X1j1eB8nrug
var (
	SendHiTypeText SendHiType = "TEXT"
	SendHiTypeLink SendHiType = "LINK"
	SendHiTypeMD   SendHiType = "MD"
	SendHiTypeAT   SendHiType = "AT"
)

type SendHiOption struct {
	Type    SendHiType
	Content string

	NeedAt    bool
	AtAll     bool
	AtUserIDs []string
}

func SendHi(url string, option SendHiOption) error {
	var robotList = []RobotArg{}
	var robotArg = RobotArg{
		Msg: RobotMsg{
			Body: []IRobotBody{},
		},
	}

	switch option.Type {
	case SendHiTypeText:
		robotArg.Msg.Body = append(robotArg.Msg.Body, &RobotBodyText{
			Type:    SendHiTypeText,
			Content: option.Content,
		})
	case SendHiTypeLink:
		// TODO:
	case SendHiTypeMD:
		robotArg.Msg.Body = append(robotArg.Msg.Body, &RobotBodyMD{
			Type:    SendHiTypeMD,
			Content: option.Content,
		})
	}

	if option.NeedAt {
		if option.Type != SendHiTypeMD {
			robotArg.Msg.Body = append(robotArg.Msg.Body, &RobotBodyAt{
				Type:      SendHiTypeAT,
				AtUserIDs: option.AtUserIDs,
				AtAll:     option.AtAll,
			})
			robotList = append(robotList, robotArg)
		} else {
			robotList = append(robotList, robotArg)
			robotAtArg := RobotArg{
				Msg: RobotMsg{
					Body: []IRobotBody{
						&RobotBodyText{
							Type:    SendHiTypeText,
							Content: "辛苦关注以上markdown内容并及时处理",
						},
						&RobotBodyAt{
							Type:      SendHiTypeAT,
							AtUserIDs: option.AtUserIDs,
							AtAll:     option.AtAll,
						},
					},
				},
			}
			robotList = append(robotList, robotAtArg)
		}
	} else {
		robotList = append(robotList, robotArg)
	}

	return sendHi(url, robotList)
}

func sendHi(url string, robotList []RobotArg) error {
	for _, msg := range robotList {
		jsonData, err := json.Marshal(msg)
		if err != nil {
			return err
		}
		req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
		if err != nil {
			return err
		}
		req.Header.Set("Content-Type", "application/json")
		client := &http.Client{}
		var resp *http.Response
		for i := 0; i < 3; i++ {
			resp, err = client.Do(req)
			if err != nil {
				continue
			}

			break
		}

		if err != nil {
			return err
		}
		defer resp.Body.Close()
	}
	return nil

}
