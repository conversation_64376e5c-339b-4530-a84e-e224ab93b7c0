/*
* Easy生成，**平台修改本地update会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: 错误码
 */
package errs

import "icode.baidu.com/baidu/netdisk/easy-go-sdk"

// 框架公共错误码
var (
	// 参数异常
	CommonParams = easy.CustomError(2, "params error", 400)
	// 返回成功
	CommonSuccess = easy.CustomError(0, "success", 200)
	// 全部失败
	CommonAllFail = easy.CustomError(500, "all fail", 500)
	// 部分失败
	CommonPartFail = easy.CustomError(400, "partial fail", 400)
)

// 本项目公共错误码
var (
	// 数据库访问错误
	AppDBErr = easy.CustomError(1001, "数据库访问错误", 200)
	// JSON解析错误
	AppJSONMarshal          = easy.CustomError(1002, "JSON解析错误", 200)
	AppDBInsertFailed       = easy.CustomError(12003, "DB insert failed", 503)
	AppDBConnectFailed      = easy.CustomError(12004, "DB connect failed", 503)
	AppDBQueryFailed        = easy.CustomError(12005, "DB query failed", 503)
	AppDBUpdateFailed       = easy.CustomError(12006, "DB update failed", 503)
	AppDatabaseQueryFailed  = easy.CustomError(12007, "Failed to fetch product information", 500)
	AppDatebaseInsertFailed = easy.CustomError(12008, "failed to insert or update", 500)
)
