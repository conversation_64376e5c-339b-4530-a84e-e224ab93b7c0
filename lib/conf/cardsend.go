/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 配置定义，热更新实现
 */
package conf

import (
	"errors"
	"sync"

	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
)

// 服务号提醒
type CardsendConfFeedbackWorker struct {
	HiEnable bool   `json:"hi_enable" `
	URL      string `json:"url" `
}

type CardsendConfSqldiffCallback struct {
	Recevier           []string `json:"recevier" `
	CallbackEnable     bool     `json:"callbackEnable" `
	CallbackUserEnable bool     `json:"callbackUserEnable" `
}

type CardsendConf struct {
	Mu                 sync.RWMutex
	fileName           string
	fileType           tangram_config.Decoder
	SqldiffReceiver    []string                     `json:"sqldiff_receiver" `
	SelfHost           string                       `json:"self_host" `
	SelfPort           int                          `json:"self_port" `
	SendUserEnable     bool                         `json:"send_user_enable" `
	NilawayReceiver    []string                     `json:"nilaway_receiver" `
	FeedbackWorker     *CardsendConfFeedbackWorker  `json:"feedbackWorker" `
	UfcGlobalProxyAddr string                       `json:"ufcGlobalProxyAddr" `
	SqldiffCallback    *CardsendConfSqldiffCallback `json:"sqldiffCallback" `
}

// 热更新Fetch
func (c *CardsendConf) Fetch(data []byte) error {
	c.Mu.Lock()
	defer c.Mu.Unlock()
	if len(data) > 0 {
		err := tangram_config.Unmarshal(c.fileType, data, c)
		if err != nil {
			tangram_logger.Error("[msg:conf Fetch error] [filename: %s] [error: %v]", c.fileName, err)
			return err
		}
	} else {
		tangram_logger.Warning("[msg:conf Fetch error] [filename: %s] [error: data is empty]", c.fileName)
		return errors.New("data is empty")
	}
	return nil
}
