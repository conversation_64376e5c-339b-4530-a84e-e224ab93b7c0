/*
* Easy生成，**平台修改本地update会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: 常量以及数据库信息引用
* Doc: https://ku.baidu-int.com/d/QFI7Jr_SnCBKKI
 */
package consts

import "icode.baidu.com/baidu/netdisk/easy-go-sdk"

// DB信息定义
type NetdiskEasyCoderule struct {
	easy.DBStruct
	Tables struct {
		EasyRuleData             easy.TableName
		EasyLastRecord           easy.TableName
		EeTotalLine              easy.TableName
		EeIncreLine              easy.TableName
		EasyRepoTotalIncreRecord easy.TableName
		EasyRepoTotalRecord      easy.TableName
		EasyFileexistRecord      easy.TableName
		EasyFnMsg                easy.TableName
		BehaviorMeasure          easy.TableName
		LintIssues               easy.TableName
		ScanBatches              easy.TableName
		SQLScanResults           easy.TableName
		SQLDiffRecord            easy.TableName
		AppDBInfo                easy.TableName
	}
}
type TangramDump struct {
	easy.DBStruct
	Tables struct {
		TangramReplayUsageStatistics   easy.TableName
		TangramReplayDefaultStatistics easy.TableName
	}
}
type FaultPanel struct {
	easy.DBStruct
	Tables struct {
		TDrdsInstance easy.TableName
	}
}

// 初始化数据库和表名的映射
var Dbs struct {
	NetdiskEasyCoderule NetdiskEasyCoderule
	TangramDump         TangramDump
	FaultPanel          FaultPanel
}

// 在 init 函数中为 Dbs 进行初始化赋值
func init() {
	Dbs.NetdiskEasyCoderule.Name = "netdisk_easy_coderule"
	Dbs.NetdiskEasyCoderule.ConfName = "mysql_cloud_netdisk_easy_coderule"
	Dbs.NetdiskEasyCoderule.Tables.EasyRuleData = "easy_rule_data"
	Dbs.NetdiskEasyCoderule.Tables.EasyLastRecord = "easy_last_record"
	Dbs.NetdiskEasyCoderule.Tables.EeTotalLine = "ee_total_line"
	Dbs.NetdiskEasyCoderule.Tables.EeIncreLine = "ee_incre_line"
	Dbs.NetdiskEasyCoderule.Tables.EasyRepoTotalIncreRecord = "easy_repo_total_incre_record"
	Dbs.NetdiskEasyCoderule.Tables.EasyRepoTotalRecord = "easy_repo_total_record"
	Dbs.NetdiskEasyCoderule.Tables.EasyFileexistRecord = "easy_fileexist_record"
	Dbs.NetdiskEasyCoderule.Tables.EasyFnMsg = "easy_fn_msg"
	Dbs.NetdiskEasyCoderule.Tables.BehaviorMeasure = "behavior_measure"
	Dbs.NetdiskEasyCoderule.Tables.LintIssues = "lint_issues"
	Dbs.NetdiskEasyCoderule.Tables.ScanBatches = "scan_batches"
	Dbs.NetdiskEasyCoderule.Tables.SQLScanResults = "sql_scan_results"
	Dbs.NetdiskEasyCoderule.Tables.SQLDiffRecord = "sql_diff_record"
	Dbs.NetdiskEasyCoderule.Tables.AppDBInfo = "app_db_info"
	Dbs.TangramDump.Name = "tangram_dump"
	Dbs.TangramDump.ConfName = "mysql_cloud_tangram_dump"
	Dbs.TangramDump.Tables.TangramReplayUsageStatistics = "tangram_replay_usage_statistics"
	Dbs.TangramDump.Tables.TangramReplayDefaultStatistics = "tangram_replay_default_statistics"
	Dbs.FaultPanel.Name = "fault-panel"
	Dbs.FaultPanel.ConfName = "mysql_cloud_fault-panel"
	Dbs.FaultPanel.Tables.TDrdsInstance = "t_drds_instance"
}
