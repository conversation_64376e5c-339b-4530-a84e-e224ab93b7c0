/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: TangramReplayDefaultStatistics实体的数据库操作单测
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package dao

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	// 注册驱动
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	// 注册直连sql驱动
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

func MockTangramReplayDefaultStatisticsRegistry(t *testing.T) {
	err := mock.MockSqlDirectRegistry("mysql_cloud_tangram_dump",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "tangram_dump"})
	assert.Nil(t, err)
}

func TestTangramReplayDefaultStatisticsAdd(t *testing.T) {
	// 注册驱动
	MockTangramReplayDefaultStatisticsRegistry(t)
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewTangramReplayDefaultStatisticsDao(easy.NewContext())
	info := &entity.TangramReplayDefaultStatistics{}
	lastInsertID, err := s.Add(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestTangramReplayDefaultStatisticsAddList(t *testing.T) {
	// 注册驱动
	MockTangramReplayDefaultStatisticsRegistry(t)
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewTangramReplayDefaultStatisticsDao(easy.NewContext())
	list := []*entity.TangramReplayDefaultStatistics{}
	err := s.AddList(list)
	assert.Nil(t, err)
}

func TestTangramReplayDefaultStatisticsUpdate(t *testing.T) {
	// 注册驱动
	MockTangramReplayDefaultStatisticsRegistry(t)
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewTangramReplayDefaultStatisticsDao(easy.NewContext())
	info := &entity.TangramReplayDefaultStatistics{}
	lastInsertID, err := s.Update(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestTangramReplayDefaultStatisticsCount(t *testing.T) {
	// 注册驱动
	MockTangramReplayDefaultStatisticsRegistry(t)
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewTangramReplayDefaultStatisticsDao(easy.NewContext())
	count, err := s.Count()
	assert.Nil(t, err)
	tangram_logger.Info("count=%+v", count)
}

// TangramReplayDefaultStatistics dao层测试入口
func TestTangramReplayDefaultStatisticsGet(t *testing.T) {
	// 注册驱动
	MockTangramReplayDefaultStatisticsRegistry(t)
	// 初始化TangramReplayDefaultStatisticsDao
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewTangramReplayDefaultStatisticsDao(easy.NewContext())
	// db执行
	info, err := s.Get(123)
	// 断言结果
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("info=%+v", info)
}

// TangramReplayDefaultStatistics dao层测试入口
func TestTangramReplayDefaultStatisticsList(t *testing.T) {
	// 注册驱动
	MockTangramReplayDefaultStatisticsRegistry(t)
	// 初始化TangramReplayDefaultStatisticsDao
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewTangramReplayDefaultStatisticsDao(easy.NewContext())
	// db执行
	list, err := s.List()
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("list=%v", string(by))
}

// TangramReplayDefaultStatistics dao层测试入口
func TestTangramReplayDefaultStatisticsPage(t *testing.T) {
	// 注册驱动
	MockTangramReplayDefaultStatisticsRegistry(t)
	// 初始化TangramReplayDefaultStatisticsDao
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewTangramReplayDefaultStatisticsDao(easy.NewContext())
	// db执行
	list, count, pages, err := s.Page(1, 10)
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("[list=%v] [count=%v] [count=%v]", string(by), count, pages)
}
