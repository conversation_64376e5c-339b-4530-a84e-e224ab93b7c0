/*
 * Author:  Easy
 * Version: 1.0.0
 * Description: SQLDiffRecord实体的数据库操作
 */
package dao

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

/*
// SQLDiffRecord数据库操作基类，用于框架依赖注入
type SQLDiffRecordDao struct {
	easy.Dao
}

/*
* SQLDiffRecordDao -手动初始化对应Dao层操作
* PARAMS:
*    ctx 上下文，初始化时必须传递
* RETURNS:
*    *SQLDiffRecordDao, 对应初始化后的Dao层结构体指针
* 注意：若初始化失败返回错误为easy.BindingError，则会返回nil
 */
func NewSQLDiffRecordDao(ctx *easy.Context) *SQLDiffRecordDao {
	dao := &SQLDiffRecordDao{}
	err := easy.Injection(dao, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("dao injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return dao
}

/*
 * Init -Dao初始化操作
 * 无论是依赖注入还是手动调用NewSQLDiffRecordDao 后都会走的逻辑
 */
func (d *SQLDiffRecordDao) Init() {
	// 在这里可以做初始化赋值内部属性操作
	// 如关闭Dao的打印SQL操作:d.DoNotPrintSQL = true
}

// Before -前置操作,在执行sql之前调用;op:操作类型
func (d *SQLDiffRecordDao) Before(op easy.Operation, model *easy.ModelStruct) error {
	return nil
}

// After -后置操作,在执行sql之后调用，error:sql执行中出现的异常
func (d *SQLDiffRecordDao) After(op easy.Operation, sql string, err error) {
	if err != nil {
		// d.SLog.Error("sql exec error").SetErr(err).Print()
	}
}

/*
 * Add -新增单条数据
 * PARAMS:
 *    info 修改的数据
 * RETURNS:
 *    lastInsertId, nil  if succeed
 *    0,error            if fail
 */
func (d *SQLDiffRecordDao) Add(info *entity.SQLDiffRecord) (lastInsertID int64, err error) {
	return d.Model(info).Create()
}

/*
 * AddList -批量添加数据
 * PARAMS:
 *    list 批量新增的的数据
 * RETURNS:
 *    nil        if succeed
 *    error      if fail
 */
func (d *SQLDiffRecordDao) AddList(list []*entity.SQLDiffRecord) (err error) {
	return d.Model(&list).CreateList()
}

/*
 * GetListByRepo -根据仓库名获取diff记录列表
 * PARAMS:
 *    repoName 仓库名
 * RETURNS:
 *    list 记录列表
 *    err 错误信息
 */
func (d *SQLDiffRecordDao) GetListByRepo(repoName string) (list []*entity.SQLDiffRecord, err error) {
	err = d.Model(&list).Where("repo=?", repoName).Select("*").OrderBy("created_at DESC").All()
	return
}

/*
 * GetListByBatchID -根据批次ID获取diff记录列表
 * PARAMS:
 *    batchID 批次ID
 * RETURNS:
 *    list 记录列表
 *    err 错误信息
 */
func (d *SQLDiffRecordDao) GetListByBatchID(batchID string) (list []*entity.SQLDiffRecord, err error) {
	err = d.Model(&list).Where("batch_id=?", batchID).Select("*").OrderBy("created_at DESC").All()
	return
}

/*
 * GetListByCommitURL -根据提交URL获取diff记录列表
 * PARAMS:
 *    commitURL 提交URL
 * RETURNS:
 *    list 记录列表
 *    err 错误信息
 */
func (d *SQLDiffRecordDao) GetListByCommitURL(repo string, commitURL string) (list []*entity.SQLDiffRecord, err error) {
	err = d.Model(&list).Where("repo=? and commit_url=?", repo, commitURL).Select("*").OrderBy("created_at DESC").All()
	return
}

/*
 * Update -修改单条数据
 * PARAMS:
 *    info 修改的数据
 *    zeroValues 可以指定值修改哪些字段
 * RETURNS:
 *    rowsAffected nil  if succeed
 *    0,error      if fail
 */
func (d *SQLDiffRecordDao) Update(info *entity.SQLDiffRecord, zeroValues ...string) (rowsAffected int64, err error) {
	return d.Model(info).Update(zeroValues...)
}

/*
 * Delete -物理删除一条数据
 * PARAMS:
 *    ID int64 主键
 * RETURNS:
 *    rowsAffected nil  if succeed
 *    0,error           if fail
 */
func (d *SQLDiffRecordDao) Delete(ID int64) (rowsAffected int64, err error) {
	info := &entity.SQLDiffRecord{}
	return d.Model(info).Where("id=?", ID).Delete()
}

/*
 * Get -通过主键获取一条数据
 * PARAMS:
 *    ID int64 主键
 * RETURNS:
 *    info nil  if succeed
 *    nil,error if fail
 */
func (d *SQLDiffRecordDao) Get(ID int64) (info *entity.SQLDiffRecord, err error) {
	info = &entity.SQLDiffRecord{}
	err, ok := d.Model(info).Where("id=?", ID).Get()
	if !ok {
		info = nil
	}
	return
}

/*
 * List -获取列表数据
 * RETURNS:
 *    list nil  if succeed
 *    nil,error if fail
 */
func (d *SQLDiffRecordDao) List() (list []*entity.SQLDiffRecord, err error) {
	err = d.Model(&list).Select("*").OrderBy("created_at DESC").All()
	return
}

/*
 * Page -获取分页列表数据
 * PARAMS:
 *    page:当前页码
 *    size:一页显示条数
 * RETURNS:
 *    list,count,pages, nil  if succeed
 *    nil,0,0,error          if fail
 */
func (d *SQLDiffRecordDao) Page(page, size int) (list []*entity.SQLDiffRecord, count int64, pages int, err error) {
	count, pages, err = d.Model(&list).Select("*").OrderBy("created_at DESC").Page(page, size)
	return
}
