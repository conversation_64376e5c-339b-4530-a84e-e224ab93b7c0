/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: TangramReplayDefaultStatistics实体的数据库操作
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package dao

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

type TangramReplayDefaultStatisticsDao struct {
	easy.Dao
}

// 初始化 必须传入上下文
func NewTangramReplayDefaultStatisticsDao(ctx *easy.Context) *TangramReplayDefaultStatisticsDao {
	dao := &TangramReplayDefaultStatisticsDao{}
	err := easy.Injection(dao, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("dao injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return dao
}

// 获取 Default表 每app最后一次执行时间的数据
func (d *TangramReplayDefaultStatisticsDao) ListAPPDataLastTime() ([]*entity.TangramReplayDefaultStatistics, error) {
	conn, err := easy.GetConnInfo("mysql_cloud_tangram_dump", d.Context)
	if err != nil {
		d.Context.SLog.Warning("get connection error").SetErr(err).Print()
		return nil, err
	}

	// 查询每个APP最后一次执行时间下的数据
	sql := "SELECT t1.id, t1.app, t1.user, t1.replay_cnt, t1.replay_success_cnt, t1.replay_failed_cnt, t1.coverage_rate, t1.use_time " +
		"FROM tangram_replay_default_statistics t1 " +
		"INNER JOIN ( SELECT app, MAX(use_time) AS max_use_time " +
		"FROM tangram_replay_default_statistics GROUP BY app ) t2 " +
		"ON t1.app = t2.app AND t1.use_time = t2.max_use_time; "

	rows, err := conn.Query(sql)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var res []*entity.TangramReplayDefaultStatistics
	for rows.Next() {
		info := &entity.TangramReplayDefaultStatistics{}
		err := rows.Scan(&info.ID, &info.App, &info.User, &info.ReplayCnt, &info.ReplaySuccessCnt, &info.ReplayFailedCnt, &info.CoverageRate, &info.UseTime)
		if err != nil {
			d.Context.SLog.Warning("rows scan error").SetErr(err).Print()
			continue
		}
		res = append(res, info)
	}

	if rows.Err() != nil {
		return nil, err
	}

	return res, nil
}

/*
 * Init -Dao初始化操作
 * 无论是依赖注入还是手动调用NewTangramReplayDefaultStatisticsDao 后都会走的逻辑
 */
func (d *TangramReplayDefaultStatisticsDao) Init() {
	// 在这里可以做初始化赋值内部属性操作
	// 如关闭Dao的打印SQL操作:d.DoNotPrintSQL = true
}

// Before -前置操作,在执行sql之前调用;op:操作类型
func (d *TangramReplayDefaultStatisticsDao) Before(op easy.Operation, model *easy.ModelStruct) error {
	// Demo1:前置拦截
	// if d.Context.GetUid() == 0 {
	// 	统一处理前置条件返回error不继续执行
	// 	return errs.AppDberror
	// }

	// Demo2: 统一处理分表规则
	// model.Table(func(table string) string {
	// return fmt.Sprintf("%s_%d", table, d.Context.GetUid()%32)
	// })

	// Demo3: 统一添加公共必带条件；add 会被过滤条件所以此处不会影响结果
	// model.Where("uid=?", d.Context.GetUid())
	return nil
}

// After -后置操作,在执行sql之后调用，error:sql执行中出现的异常
func (d *TangramReplayDefaultStatisticsDao) After(op easy.Operation, sql string, err error) {
	if err != nil {
		// d.SLog.Error("sql exec error").SetErr(err).Print()
	}
}

/*
 * Add -新增单条数据 如果info有关联数据会
 * 1.开启事务 2.insert info 3.将info的主键或者自增键复制给关联 4.insert关联数据
 * PARAMS:
 *    info 修改的数据
 * RETURNS:
 *    lastInsertId, nil  if succeed
 *    0,error            if fail
 */
func (d *TangramReplayDefaultStatisticsDao) Add(info *entity.TangramReplayDefaultStatistics) (lastInsertID int64, err error) {
	return d.Model(info).Create()
}

/*
 * AddList -批量添加数据 1.开启事务 2.开启协程池 3.批量添加 4.操作关联数据 5.check异常 6.提交或回滚事务
 * PARAMS:
 *    list 批量新增的的数据
 * RETURNS:
 *    nil        if succeed
 *    error      if fail
 */
func (d *TangramReplayDefaultStatisticsDao) AddList(list []*entity.TangramReplayDefaultStatistics) (err error) {
	return d.Model(&list).CreateList()
}

/*
 * Update -修改单条数据，支持批量修改:Model(info).Where("xx=?",xx)
 * PARAMS:
 *    info 修改的数据
 *    zeroValues 可以指定值修改哪些字段
 * RETURNS:
 *    rowsAffected nil  if succeed
 *    0,error      if fail
 */
func (d *TangramReplayDefaultStatisticsDao) Update(info *entity.TangramReplayDefaultStatistics, zeroValues ...string) (rowsAffected int64, err error) {
	// 默认不传where 条件会加入主键进行筛选
	return d.Model(info).Update(zeroValues...)
}

/*
 * Delete -物理删除一条数据，可以根据业务自定义where条件和传入参数
 * PARAMS:
 *    ID int32 主键
 * RETURNS:
 *    rowsAffected nil  if succeed
 *    0,error           if fail
 */
func (d *TangramReplayDefaultStatisticsDao) Delete(ID int32) (rowsAffected int64, err error) {
	// 默认不传where 条件会加入主键进行筛选
	info := &entity.TangramReplayDefaultStatistics{}
	return d.Model(info).Where("id=?", ID).Delete()
}

/*
 * Count -获取数据条数，可以根据业务自定义where条件和传入参数
 * PARAMS:
 * RETURNS:
 *    count nil    if succeed
 *    0, error     if fail
 */
func (d *TangramReplayDefaultStatisticsDao) Count() (count int64, err error) {
	return d.Model(&entity.TangramReplayDefaultStatistics{}).Count()
}

/*
 * Get -通过主键获取一条数据，可以根据业务自定义where条件和传入参数
 * PARAMS:
 *    ID int32 主键
 * RETURNS:
 *    info nil  if succeed
 *    nil,error if fail
 */
func (d *TangramReplayDefaultStatisticsDao) Get(ID int32) (info *entity.TangramReplayDefaultStatistics, err error) {
	info = &entity.TangramReplayDefaultStatistics{}
	err, ok := d.Model(info).Where("id=?", ID).Get()
	if !ok {
		info = nil
	}
	return
}

/*
 * List -获取列表数据，可以根据业务自定义where条件和传入参数
 * PARAMS:
 * RETURNS:
 *    list nil  if succeed
 *    nil,error if fail
 */
func (d *TangramReplayDefaultStatisticsDao) List() (list []*entity.TangramReplayDefaultStatistics, err error) {
	// Relation是关联查询 默认是筛选外键关联info主键的所以列表，也可以自定义查询操作：b.Select().Where().OrderBy().Offset().Limit()
	err = d.Model(&list).Select("*").
		All()
	return
}

/*
 * Page -获取分页列表数据，可以根据业务自定义where条件和传入参数
 * PARAMS:
 *    page:当前页码
 *    size:一页显示条数
 *    count:根据条件筛选后的总条数
 *    pages:根据条件筛选后的总页数
 *    list :分页获取的数据条目
 * RETURNS:
 *    list,count,pages, nil  if succeed
 *    nil,0,0,error          if fail
 */
func (d *TangramReplayDefaultStatisticsDao) Page(page, size int) (list []*entity.TangramReplayDefaultStatistics, count int64, pages int, err error) {
	count, pages, err = d.Model(&list).Select("*").Page(page, size)
	return
}
