/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: EasyRuleData实体的数据库操作单测
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package dao

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

// EasyRuleData dao层TestSQL测试入口
func TestEasyRuleDataTestSQL(t *testing.T) {
	// 注册驱动
	MockEasyRuleDataRegistry(t)
	// 初始化EasyRuleDataDao
	s := NewEasyRuleDataDao(easy.NewContext())
	// db执行
	list, err := s.TestSQL("", 0)
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("list=%v", string(by))
}

// EasyRuleData dao层EasyRuleDataSQL测试入口v1
func TestEasyRuleDataEasyRuleDataSQL(t *testing.T) {
	// 注册驱动
	MockEasyRuleDataRegistry(t)
	s := NewEasyRuleDataDao(easy.NewContext())
	info := &entity.EasyRuleData{}
	lastInsertID, err := s.EasyRuleDataSQL(info,
		0, "", 0)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}
