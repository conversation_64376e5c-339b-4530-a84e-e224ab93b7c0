/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: EasyRepoTotalRecord实体的数据库操作单测
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package dao

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

func TestEasyRepoTotalRecordAdd(t *testing.T) {
	MockEasyRepoTotalRecordRegistry(t)
	s := NewEasyRepoTotalRecordDao(easy.NewContext())
	info := &entity.EasyRepoTotalRecord{}
	lastInsertID, err := s.Add(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestEasyRepoTotalRecordAddList(t *testing.T) {
	MockEasyRepoTotalRecordRegistry(t)
	s := NewEasyRepoTotalRecordDao(easy.NewContext())
	list := []*entity.EasyRepoTotalRecord{}
	err := s.AddList(list)
	assert.Nil(t, err)
}

func TestEasyRepoTotalRecordUpdate(t *testing.T) {
	MockEasyRepoTotalRecordRegistry(t)
	s := NewEasyRepoTotalRecordDao(easy.NewContext())
	info := &entity.EasyRepoTotalRecord{}
	lastInsertID, err := s.Update(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestEasyRepoTotalRecordCount(t *testing.T) {
	MockEasyRepoTotalRecordRegistry(t)
	s := NewEasyRepoTotalRecordDao(easy.NewContext())
	count, err := s.Count()
	assert.Nil(t, err)
	tangram_logger.Info("count=%+v", count)
}

func TestEasyRepoTotalRecordGet(t *testing.T) {
	MockEasyRepoTotalRecordRegistry(t)
	s := NewEasyRepoTotalRecordDao(easy.NewContext())
	info, err := s.Get(123)
	assert.Nil(t, err)
	tangram_logger.Info("info=%+v", info)
}

func TestEasyRepoTotalRecordList(t *testing.T) {
	MockEasyRepoTotalRecordRegistry(t)
	s := NewEasyRepoTotalRecordDao(easy.NewContext())
	time := int64(134122342423)
	list, err := s.List(time)
	assert.Nil(t, err)
	by, err := json.Marshal(&list)
	assert.Nil(t, err)
	tangram_logger.Info("list=%v", string(by))
}

func TestEasyRepoTotalRecordPage(t *testing.T) {
	MockEasyRepoTotalRecordRegistry(t)
	s := NewEasyRepoTotalRecordDao(easy.NewContext())
	list, count, pages, err := s.Page(1, 10)
	assert.Nil(t, err)
	by, err := json.Marshal(&list)
	assert.Nil(t, err)
	tangram_logger.Info("[list=%v] [count=%v] [count=%v]", string(by), count, pages)
}

// 注册驱动
func MockEasyRepoTotalRecordRegistry(t *testing.T) {
	// 注册直连sql驱动
	err := mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
}
