/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: EeIncreLine实体的数据库操作单测
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package dao

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

func TestEeIncreLineAdd(t *testing.T) {
	// 注册驱动
	MockEeIncreLineRegistry(t)
	s := NewEeIncreLineDao(easy.NewContext())
	info := &entity.EeIncreLine{}
	lastInsertID, err := s.Add(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestEeIncreLineAddList(t *testing.T) {
	// 注册驱动
	MockEeIncreLineRegistry(t)
	s := NewEeIncreLineDao(easy.NewContext())
	list := []*entity.EeIncreLine{}
	err := s.AddList(list)
	assert.Nil(t, err)
}

func TestEeIncreLineUpdate(t *testing.T) {
	// 注册驱动
	MockEeIncreLineRegistry(t)
	s := NewEeIncreLineDao(easy.NewContext())
	info := &entity.EeIncreLine{}
	lastInsertID, err := s.Update(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestEeIncreLineCount(t *testing.T) {
	// 注册驱动
	MockEeIncreLineRegistry(t)
	s := NewEeIncreLineDao(easy.NewContext())
	count, err := s.Count()
	assert.Nil(t, err)
	tangram_logger.Info("count=%+v", count)
}

// EeIncreLine dao层测试入口
func TestEeIncreLineGet(t *testing.T) {
	// 注册驱动
	MockEeIncreLineRegistry(t)
	// 初始化EeIncreLineDao
	s := NewEeIncreLineDao(easy.NewContext())
	// db执行
	info, err := s.Get(123)
	// 断言结果
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("info=%+v", info)
}

// EeIncreLine dao层测试入口
func TestEeIncreLineList(t *testing.T) {
	// 注册驱动
	MockEeIncreLineRegistry(t)
	// 初始化EeIncreLineDao
	s := NewEeIncreLineDao(easy.NewContext())
	// db执行
	startTime := int64(111)
	endTime := int64(222)
	list, err := s.List(startTime, endTime)
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("list=%v", string(by))
}

// EeIncreLine dao层测试入口
func TestEeIncreLinePage(t *testing.T) {
	// 注册驱动
	MockEeIncreLineRegistry(t)
	// 初始化EeIncreLineDao
	s := NewEeIncreLineDao(easy.NewContext())
	// db执行
	list, count, pages, err := s.Page(1, 10)
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("[list=%v] [count=%v] [count=%v]", string(by), count, pages)
}

// 注册驱动
func MockEeIncreLineRegistry(t *testing.T) {
	// 注册直连sql驱动
	err := mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		// mock数据库连接地址，该地址默认为业务在平台上配置的数据库test环境下的地址，若有变更记得更改
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
}
