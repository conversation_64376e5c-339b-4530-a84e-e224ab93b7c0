/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: StorageConfigHTTPRetry实体的数据库操作
 * SQL 编辑器设计SQL自动同步代码
 * Dao层error断言技巧：
 * 框架error断言：
 *    - if errors.IsConnectionErr(err) DB连接错误
 *    - if errors.IsAccessDeniedErr(err) 访问被拒绝错误
 *    - if errors.IsCtxCancelErr(err) 上下文超时错误
 *    - if errors.IsTooManyConnectionsErr(err) 连接数过多错误
 *    - if errors.IsSyntaxErr(err) 语法错误
 * 1.insert：result, err := model.CreateR()
 *    - if errors.IsDuplicateKeyErr() 主键重复错误，可以使用DupKeyUpdate避免
 *    - if result.RowsAffected()) 获取受影响的行数，以确定插入操作是否成功
 * 2.delete：result, err := model.DeleteR()
 *    - if result.RowsAffected()) 获取受影响的行数，以确定删除操作是否成功
 * 3.update：result, err := model.UpdateR()
 *    - if result.RowsAffected()) 获取受影响的行数，以确定更新操作是否成功
 * 4.select：list,err := model.All()多条； info,err := model.Get()单条
 *    - if len(list) == 0 查询数据是否为空
 *    - if info == nil 单条查询数据是否为空
 */

package dao

import (
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

// TestSQL 测试sql
func (d *EasyRuleDataDao) TestSQL(username string, limit int) (list []*entity.EasyRuleData, err error) {
	model := d.Model(&list)
	model.Select("*")
	model.Where("username = ?", username)
	model.Limit(limit)
	err = model.All()
	return
}

// EasyRuleDataSQL 动态更新函数
func (d *EasyRuleDataDao) EasyRuleDataSQL(info *entity.EasyRuleData,
	ID int64, username string, createAt int64) (rows int64, err error) {
	model := d.Model(info)
	model.Where("id = ? AND username = ?", ID, username)
	return model.Update("username", "create_at")
}
