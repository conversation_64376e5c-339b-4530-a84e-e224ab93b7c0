/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: AppDBInfo实体的数据库操作单测
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package dao

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

func TestAppDBInfoAdd(t *testing.T) {
	// 注册驱动
	MockAppDBInfoRegistry(t)
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewAppDBInfoDao(easy.NewContext())
	info := &entity.AppDBInfo{}
	lastInsertID, err := s.Add(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestAppDBInfoAddList(t *testing.T) {
	// 注册驱动
	MockAppDBInfoRegistry(t)
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewAppDBInfoDao(easy.NewContext())
	list := []*entity.AppDBInfo{}
	err := s.AddList(list)
	assert.Nil(t, err)
}

func TestAppDBInfoUpdate(t *testing.T) {
	// 注册驱动
	MockAppDBInfoRegistry(t)
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewAppDBInfoDao(easy.NewContext())
	info := &entity.AppDBInfo{}
	lastInsertID, err := s.Update(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestAppDBInfoCount(t *testing.T) {
	// 注册驱动
	MockAppDBInfoRegistry(t)
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewAppDBInfoDao(easy.NewContext())
	count, err := s.Count()
	assert.Nil(t, err)
	tangram_logger.Info("count=%+v", count)
}

// AppDBInfo dao层测试入口
func TestAppDBInfoGet(t *testing.T) {
	// 注册驱动
	MockAppDBInfoRegistry(t)
	// 初始化AppDBInfoDao
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewAppDBInfoDao(easy.NewContext())
	// db执行
	info, err := s.Get(123)
	// 断言结果
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("info=%+v", info)
}

// AppDBInfo dao层测试入口
func TestAppDBInfoList(t *testing.T) {
	// 注册驱动
	MockAppDBInfoRegistry(t)
	// 初始化AppDBInfoDao
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewAppDBInfoDao(easy.NewContext())
	// db执行
	list, err := s.List()
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("list=%v", string(by))
}

// AppDBInfo dao层测试入口
func TestAppDBInfoPage(t *testing.T) {
	// 注册驱动
	MockAppDBInfoRegistry(t)
	// 初始化AppDBInfoDao
	// 需要注意：如果导入的表不含主键，则不会生成对应的Dao文件，此处会飘红，按照DBA规范，表结构至少要有主键
	s := NewAppDBInfoDao(easy.NewContext())
	// db执行
	list, count, pages, err := s.Page(1, 10)
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("[list=%v] [count=%v] [count=%v]", string(by), count, pages)
}

// 注册驱动
func MockAppDBInfoRegistry(t *testing.T) {
	// 注册直连sql驱动
	err := mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		// mock数据库连接地址，该地址默认为业务在平台上配置的数据库test环境下的地址，若有变更记得更改
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
}
