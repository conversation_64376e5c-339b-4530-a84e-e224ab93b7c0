/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: StorageConfigHTTPRetry实体的数据库操作
 * SQL 编辑器设计SQL自动同步代码
 * Dao层error断言技巧：
 * 框架error断言：
 *    - if errors.IsConnectionErr(err) DB连接错误
 *    - if errors.IsAccessDeniedErr(err) 访问被拒绝错误
 *    - if errors.IsCtxCancelErr(err) 上下文超时错误
 *    - if errors.IsTooManyConnectionsErr(err) 连接数过多错误
 *    - if errors.IsSyntaxErr(err) 语法错误
 * 1.insert：result, err := model.CreateR()
 *    - if errors.IsDuplicateKeyErr() 主键重复错误，可以使用DupKeyUpdate避免
 *    - if result.RowsAffected()) 获取受影响的行数，以确定插入操作是否成功
 * 2.delete：result, err := model.DeleteR()
 *    - if result.RowsAffected()) 获取受影响的行数，以确定删除操作是否成功
 * 3.update：result, err := model.UpdateR()
 *    - if result.RowsAffected()) 获取受影响的行数，以确定更新操作是否成功
 * 4.select：list,err := model.All()多条； info,err := model.Get()单条
 *    - if len(list) == 0 查询数据是否为空
 *    - if info == nil 单条查询数据是否为空
 */

package dao

import (
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

// GetIssueByFingerprintList 获取 fingerprint 相关数据
func (d *LintIssuesDao) GetIssueByFingerprintList(fingerprint []string) (list []*entity.LintIssues, err error) {
	model := d.Model(&list)
	model.Select("*")
	model.Where("fingerprint IN (?)", fingerprint)
	err = model.All()
	return
}
