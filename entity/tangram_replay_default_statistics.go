/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_tangram_dump.tangram_replay_default_statistics数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// tangram_replay_default_statistics表对应的实体
type TangramReplayDefaultStatistics struct {
	easy.Entity `db_name:"mysql_cloud_tangram_dump" table_name:"tangram_replay_default_statistics"`
	// id字段
	ID int32 `json:"id" sql:"id" pk:"2"`
	// app name
	App string `json:"app" sql:"app"`
	// user字段
	User string `json:"user" sql:"user"`
	// replay_cnt字段
	ReplayCnt int32 `json:"replay_cnt" sql:"replay_cnt"`
	// replay_success_cnt字段
	ReplaySuccessCnt int32 `json:"replay_success_cnt" sql:"replay_success_cnt"`
	// replay_failed_cnt字段
	ReplayFailedCnt int32 `json:"replay_failed_cnt" sql:"replay_failed_cnt"`
	// coverage_rate字段
	CoverageRate float32 `json:"coverage_rate" sql:"coverage_rate"`
	// use_time字段
	UseTime string `json:"use_time" sql:"use_time"`
}

func (t *TangramReplayDefaultStatistics) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
