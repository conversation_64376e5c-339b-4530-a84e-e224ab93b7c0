/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.lint_issues数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// lint_issues表对应的实体
type LintIssues struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"lint_issues"`
	// id
	ID int32 `json:"id" sql:"id" pk:"2"`
	// repo_name
	RepoName string `json:"repo_name" sql:"repo_name"`
	// rel_file_path
	RelFilePath string `json:"rel_file_path" sql:"rel_file_path"`
	// fingerprint
	Fingerprint string `json:"fingerprint" sql:"fingerprint"`
	// message
	Message string `json:"message" sql:"message"`
	// line
	Line int32 `json:"line" sql:"line"`
	// col
	Col int32 `json:"col" sql:"col"`
	// code_context
	CodeContext string `json:"code_context" sql:"code_context"`
	// status
	Status string `json:"status" sql:"status"`
	// username
	Username string `json:"username" sql:"username"`
	// created_at
	CreatedAt string `json:"created_at" sql:"created_at"`
	// first_seen_at
	FirstSeenAt string `json:"first_seen_at" sql:"first_seen_at"`
	// last_seen_at
	LastSeenAt string `json:"last_seen_at" sql:"last_seen_at"`
	// last_batch_uid
	LastBatchUID string `json:"last_batch_uid" sql:"last_batch_uid"`
	// ignored
	Ignored int32 `json:"ignored" sql:"ignored"`
	// commit_url
	CommitURL string `json:"commit_url" sql:"commit_url"`
	// rd
	Rd string `json:"rd" sql:"rd"`
	//  处理状态：0=未check, 1=是问题, 2=不是问题
	ProcessStatus int32 `json:"process_status" sql:"process_status"`
	// 处理理由
	ProcessReason string `json:"process_reason" sql:"process_reason"`
	// 处理人
	ProcessUser string `json:"process_user" sql:"process_user"`
	// 处理时间
	ProcessTime string `json:"process_time" sql:"process_time"`
}

func (t *LintIssues) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
