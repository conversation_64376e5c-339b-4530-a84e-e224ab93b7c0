/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const TangramConfigName = "TangramConfig"

// TangramConfig请求client
type TangramConfigClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewTangramConfigClient(context *easy.Context) *TangramConfigClient {
	client := &TangramConfigClient{}
	client.Name = TangramConfigName
	client.Context = context
	return client
}

// get 参数封装结构
type TangramConfigGet struct {
	Method      string `json:"method"`
	Type        string `json:"type"`
	ServiceName string `json:"service_name"`
}

// header 参数封装结构
type TangramConfigHeader struct {
	AMISUSER string `json:"AMIS_USER"`
}

// 返回结果封装结构
type TangramConfigResultDataRowsAddressing struct {
	easy.BaseDto
	From    string `json:"from"`
	To      string `json:"to"`
	Type    string `json:"type"`
	Version string `json:"version"`
}

func (t *TangramConfigResultDataRowsAddressing) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type TangramConfigResultDataRowsInteractiveConnection struct {
	easy.BaseDto
	MaxActive       int    `json:"max_active"`
	MaxConnLifetime string `json:"max_conn_lifetime"`
	MaxIdle         int    `json:"max_idle"`
	UsePool         bool   `json:"use_pool"`
	Wait            bool   `json:"wait"`
}

func (t *TangramConfigResultDataRowsInteractiveConnection) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type TangramConfigResultDataRowsRetrysItem struct {
	easy.BaseDto
	MysqlRetryConnect    int    `json:"mysql_retry_connect"`
	MysqlRetryMax        int    `json:"mysql_retry_max"`
	MysqlRetryName       string `json:"mysql_retry_name"`
	MysqlRetryReadwrite  int    `json:"mysql_retry_readwrite"`
	MysqlServicename     string `json:"mysql_servicename"`
	MysqlTimeoutConn     string `json:"mysql_timeout_conn"`
	MysqlTimeoutRead     string `json:"mysql_timeout_read"`
	MysqlTimeoutWrite    string `json:"mysql_timeout_write"`
	MysqldialoptDatabase string `json:"mysqldialopt_database"`
	MysqldialoptParam    string `json:"mysqldialopt_param"`
	MysqldialoptPassword string `json:"mysqldialopt_password"`
	MysqldialoptUsername string `json:"mysqldialopt_username"`
}

func (t *TangramConfigResultDataRowsRetrysItem) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type TangramConfigResultDataRowsRetrys struct {
	easy.BaseDto
	AddressType       string                                 `json:"address_type"`
	BnsPortName       string                                 `json:"bns_port_name"`
	BnsUpdateInterval string                                 `json:"bns_update_interval"`
	Gname             string                                 `json:"gname"`
	GroupName         string                                 `json:"groupName"`
	ID                int                                    `json:"id"`
	Item              *TangramConfigResultDataRowsRetrysItem `json:"item"`
	Penetrate         string                                 `json:"penetrate"`
	RetryService      string                                 `json:"retry_service"`
	RetryType         string                                 `json:"retry_type"`
	Suffix            string                                 `json:"suffix"`
	To                string                                 `json:"to"`
}

func (t *TangramConfigResultDataRowsRetrys) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type TangramConfigResultDataRows struct {
	easy.BaseDto
	Addressing            *TangramConfigResultDataRowsAddressing            `json:"addressing"`
	Creator               string                                            `json:"creator"`
	Ctime                 int                                               `json:"ctime"`
	Gname                 string                                            `json:"gname"`
	Group                 string                                            `json:"group"`
	ID                    int64                                             `json:"id"`
	InteractiveConnection *TangramConfigResultDataRowsInteractiveConnection `json:"interactive_connection"`
	InteractiveDepend     string                                            `json:"interactive_depend"`
	InteractiveService    string                                            `json:"interactive_service"`
	InteractiveTypeconfig string                                            `json:"interactive_typeconfig"`
	Retrys                []*TangramConfigResultDataRowsRetrys              `json:"retrys"`
	Subsuffix             []string                                          `json:"subsuffix"`
	Suffix                []string                                          `json:"suffix"`
}

func (t *TangramConfigResultDataRows) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type TangramConfigResultData struct {
	easy.BaseDto
	Count int                            `json:"count"`
	Rows  []*TangramConfigResultDataRows `json:"rows"`
}

func (t *TangramConfigResultData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type TangramConfigResult struct {
	easy.UfcResult
	Data      *TangramConfigResultData `json:"data"`
	Errmsg    string                   `json:"errmsg"`
	Errno     int                      `json:"errno"`
	Msg       string                   `json:"msg"`
	RequestID string                   `json:"request_id"`
	Status    int                      `json:"status"`
}

func (t *TangramConfigResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *TangramConfigClient) Do(
	query *TangramConfigGet,
	header *TangramConfigHeader) (*TangramConfigResult, error) {
	t.Header = header
	t.Query = query
	res := &TangramConfigResult{}
	err := t.UfcRequest(res)
	return res, err
}
