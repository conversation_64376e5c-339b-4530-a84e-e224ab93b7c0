/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const EasycodeName = "Easycode"

// Easycode请求client
type EasycodeClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewEasycodeClient(context *easy.Context) *EasycodeClient {
	client := &EasycodeClient{}
	client.Name = EasycodeName
	client.Context = context
	return client
}

// get 参数封装结构
type EasycodeGet struct {
	Icode string `json:"icode"`
}

// 返回结果封装结构
type EasycodeResult struct {
	easy.UfcResult
	Data map[string]interface{} `json:"data"`
}

func (t *EasycodeResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *EasycodeClient) Do(
	query *EasycodeGet) (*EasycodeResult, error) {
	t.Query = query
	res := &EasycodeResult{}
	err := t.UfcRequest(res)
	return res, err
}
