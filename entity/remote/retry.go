/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const RetryName = "Retry"

// Retry请求client
type RetryClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewRetryClient(context *easy.Context) *RetryClient {
	client := &RetryClient{}
	client.Name = RetryName
	client.Context = context
	return client
}

// get 参数封装结构
type RetryGet struct {
	UID        uint64 `json:"uid"`
	PortrayID  uint64 `json:"portray_id"`
	ClientType int    `json:"client_type"`
	DpUID      uint64 `json:"dp_uid"`
	AppID      uint64 `json:"app_id"`
}

// 返回结果封装结构
type RetryResult struct {
	easy.UfcResult
	PortrayID uint64 `json:"portray_id"`
}

func (t *RetryResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *RetryClient) Do(
	query *RetryGet) (*RetryResult, error) {
	t.Query = query
	res := &RetryResult{}
	err := t.UfcRequest(res)
	return res, err
}
