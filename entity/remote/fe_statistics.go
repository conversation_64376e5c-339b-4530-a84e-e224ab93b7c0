/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const FeStatisticsName = "FeStatistics"

// FeStatistics请求client
type FeStatisticsClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewFeStatisticsClient(context *easy.Context) *FeStatisticsClient {
	client := &FeStatisticsClient{}
	client.Name = FeStatisticsName
	client.Context = context
	return client
}

// get 参数封装结构
type FeStatisticsGet struct {
	St int64 `json:"st"`
	Et int64 `json:"et"`
}

// header 参数封装结构
type FeStatisticsHeader struct {
	ContentType string `json:"Content-Type"`
}

// 返回结果封装结构
type FeStatisticsResultData struct {
	easy.BaseDto
	CreateAt  int64  `json:"create_at"`
	IncreLine int32  `json:"incre_line"`
	Lang      string `json:"lang"`
	Repo      string `json:"repo"`
	User      string `json:"user"`
}

func (t *FeStatisticsResultData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type FeStatisticsResult struct {
	easy.UfcResult
	Code int                       `json:"code"`
	Data []*FeStatisticsResultData `json:"data"`
	Msg  string                    `json:"msg"`
}

func (t *FeStatisticsResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *FeStatisticsClient) Do(
	query *FeStatisticsGet,
	header *FeStatisticsHeader) (*FeStatisticsResult, error) {
	t.Header = header
	t.Query = query
	res := &FeStatisticsResult{}
	err := t.UfcRequest(res)
	return res, err
}
