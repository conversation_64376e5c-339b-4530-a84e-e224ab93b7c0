/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const CardSendName = "CardSend"

// CardSend请求client
type CardSendClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewCardSendClient(context *easy.Context) *CardSendClient {
	client := &CardSendClient{}
	client.Name = CardSendName
	client.Context = context
	return client
}

// post 参数封装结构
type CardSendPostUserDataURLURLContentsMobile struct {
	OpenType string `json:"open_type"`
	URL      string `json:"url"`
}

type CardSendPostUserDataURLURLContentsPc struct {
	OpenType string `json:"open_type"`
	URL      string `json:"url"`
}

type CardSendPostUserDataURLURLContents struct {
	Mobile *CardSendPostUserDataURLURLContentsMobile `json:"mobile"`
	Pc     *CardSendPostUserDataURLURLContentsPc     `json:"pc"`
	Title  string                                    `json:"title"`
}

type CardSendPostUserDataURLURL struct {
	Contents *CardSendPostUserDataURLURLContents `json:"contents"`
	Type     string                              `json:"type"`
}

type CardSendPostUserDataURL struct {
	URL *CardSendPostUserDataURLURL `json:"url"`
}

type CardSendPostUserData struct {
	Desc        string                   `json:"desc"`
	ServiceName string                   `json:"service_name"`
	Subtitle    string                   `json:"subtitle"`
	Title       string                   `json:"title"`
	URL         *CardSendPostUserDataURL `json:"url"`
}

type CardSendPost struct {
	AppName      string                `json:"app_name"`
	ReceiverID   string                `json:"receiver_id"`
	ReceiverType string                `json:"receiver_type"`
	TemplateID   string                `json:"template_id"`
	UserData     *CardSendPostUserData `json:"user_data"`
	UserMsg      bool                  `json:"user_msg"`
}

// header 参数封装结构
type CardSendHeader struct {
	ContentType string `json:"Content-Type"`
}

// 返回结果封装结构

// 执行函数发起请求
func (t *CardSendClient) Do(
	body *CardSendPost,
	header *CardSendHeader) error {
	t.Header = header
	t.Body = body

	err := t.UfcRequest(nil)
	return err
}
