/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const AppListName = "AppList"

// AppList请求client
type AppListClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewAppListClient(context *easy.Context) *AppListClient {
	client := &AppListClient{}
	client.Name = AppListName
	client.Context = context
	return client
}

// post 参数封装结构

// 返回结果封装结构
type AppListResultDataList struct {
	easy.BaseDto
	Name  string `json:"name"`
	Icode string `json:"icode"`
	Owner string `json:"owner"`
}

func (t *AppListResultDataList) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type AppListResultData struct {
	easy.BaseDto
	Total int                      `json:"total"`
	List  []*AppListResultDataList `json:"list"`
}

func (t *AppListResultData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type AppListResult struct {
	easy.UfcResult
	Data *AppListResultData `json:"data"`
}

func (t *AppListResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *AppListClient) Do() (*AppListResult, error) {
	res := &AppListResult{}
	err := t.UfcRequest(res)
	return res, err
}
