/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.easy_fn_msg数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// easy_fn_msg表对应的实体
type EasyFnMsg struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"easy_fn_msg"`
	// id字段
	ID int32 `json:"id" sql:"id" pk:"2"`
	// repo字段
	Repo string `json:"repo" sql:"repo"`
	// branch字段
	Branch string `json:"branch" sql:"branch"`
	// commit_at字段
	CommitAt string `json:"commit_at" sql:"commit_at"`
	// commit_id字段
	CommitID string `json:"commit_id" sql:"commit_id"`
	// create_at字段
	CreateAt int64 `json:"create_at" sql:"create_at"`
	// data字段
	Data string `json:"data" sql:"data"`
}

func (t *EasyFnMsg) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
