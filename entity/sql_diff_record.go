/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.sql_diff_record数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// sql_diff_record表对应的实体
type SQLDiffRecord struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"sql_diff_record"`
	// 主键 ID
	ID int64 `json:"id" sql:"id" pk:"2"`
	// 代码仓库
	Repo string `json:"repo" sql:"repo"`
	// 负责人
	Rd string `json:"rd" sql:"rd"`
	// 提交链接
	CommitURL string `json:"commit_url" sql:"commit_url"`
	// 唯一标识
	UnicKey string `json:"unic_key" sql:"unic_key"`
	// 旧 SQL 内容
	OldSQLText string `json:"old_sql_text" sql:"old_sql_text"`
	// 新 SQL 内容
	NewSQLText string `json:"new_sql_text" sql:"new_sql_text"`
	// 旧文件名
	OldFilename string `json:"old_filename" sql:"old_filename"`
	// 新文件名
	NewFilename string `json:"new_filename" sql:"new_filename"`
	// 旧函数名
	OldFnName string `json:"old_fn_name" sql:"old_fn_name"`
	// 新函数名
	NewFnName string `json:"new_fn_name" sql:"new_fn_name"`
	// 旧关键字段
	OldKeyName string `json:"old_key_name" sql:"old_key_name"`
	// 新关键字段
	NewKeyName string `json:"new_key_name" sql:"new_key_name"`
	// 旧类型
	OldType string `json:"old_type" sql:"old_type"`
	// 新类型
	NewType string `json:"new_type" sql:"new_type"`
	// 批次 ID
	BatchID string `json:"batch_id" sql:"batch_id"`
	// 创建时间
	CreatedAt string `json:"created_at" sql:"created_at"`
	// 更新时间
	UpdatedAt string `json:"updated_at" sql:"updated_at"`
	// 0=未review, 1=已review无问题, 2=已review有问题
	ReviewStatus int32 `json:"review_status" sql:"review_status"`
	// review时间
	ReviewTime string `json:"review_time" sql:"review_time"`
	// review用户
	Reviewer string `json:"reviewer" sql:"reviewer"`
	// html 格式展示 diff
	DiffSQLHTML string `json:"diff_sql_html" sql:"diff_sql_html"`
	// new_instance_name
	NewInstanceName string `json:"new_instance_name" sql:"new_instance_name"`
	// old_instance_name
	OldInstanceName string `json:"old_instance_name" sql:"old_instance_name"`
	// new_db_name
	NewDBName string `json:"new_db_name" sql:"new_db_name"`
	// old_db_name
	OldDBName string `json:"old_db_name" sql:"old_db_name"`
	// extra
	Extra string `json:"extra" sql:"extra"`
}

func (t *SQLDiffRecord) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
