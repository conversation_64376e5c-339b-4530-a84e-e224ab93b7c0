/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_fault-panel.t_drds_instance数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// t_drds_instance表对应的实体
type TDrdsInstance struct {
	easy.Entity `db_name:"mysql_cloud_fault-panel" table_name:"t_drds_instance"`
	// id字段
	ID int32 `json:"id" sql:"id" pk:"2"`
	// name字段
	Name string `json:"name" sql:"name"`
	// product字段
	Product string `json:"product" sql:"product"`
	// account_id字段
	AccountID string `json:"account_id" sql:"account_id"`
	// idc字段
	Idc string `json:"idc" sql:"idc"`
	// type字段
	Type string `json:"type" sql:"type"`
	// access字段
	Access string `json:"access" sql:"access"`
	// bmi_mode字段
	BmiMode string `json:"bmi_mode" sql:"bmi_mode"`
	// ctime字段
	Ctime int64 `json:"ctime" sql:"ctime"`
	// mtime字段
	Mtime int64 `json:"mtime" sql:"mtime"`
}

func (t *TDrdsInstance) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
