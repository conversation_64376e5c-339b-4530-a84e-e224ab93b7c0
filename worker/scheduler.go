/*
* Easy生成，**平台修改本地update不会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: 定时任务handler
 */
package worker

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// 定时任务处理的handler
func (s *CodeMetricSendToMailScheduler) Handler() {
	ctx := easy.NewContext()

	ctx.SLog.Info("sendworker handler start...").Print()
	sendMailWorker := NewSendMailWorker(ctx)
	if err := sendMailWorker.Validate(); err != nil {
		ctx.SLog.Error("worker: send hi task failed").SetErr(err).Print()
		return
	}

	if err := sendMailWorker.InitLastRepoInfos(); err != nil {
		ctx.SLog.Error("worker: init last repo info failed").SetErr(err).Print()
		return
	}
	if err := sendMailWorker.InitAllRepoInfos(); err != nil {
		ctx.SLog.Error("worker: init all repo info failed").SetErr(err).Print()
		return
	}

	if err := sendMailWorker.InitToEEIncreDataInfoBackend(); err != nil {
		ctx.SLog.Error("worker: init to ee incr data info failed").Set("type", "backend").SetErr(err).Print()
		return
	}

	if err := sendMailWorker.InitToEEIncreDataInfoFront(); err != nil {
		ctx.SLog.Error("worker: init to ee incr data info failed").Set("type", "front").SetErr(err).Print()
		return
	}

	if err := sendMailWorker.SendToMail(); err != nil {
		ctx.SLog.Error("worker: send mail task failed").SetErr(err).Print()
		return
	}

	ctx.SLog.Info("sendworker handler success").Print()
}
