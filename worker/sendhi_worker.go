package worker

import (
	"bytes"
	"errors"
	"fmt"
	"html/template"
	"net/smtp"
	"sort"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity/remote"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/easyutils"
)

type SendMailWorker struct {
	Context                     *easy.Context
	EasyLastRecordDao           *dao.EasyLastRecordDao
	EasyRepoTotalRecordDao      *dao.EasyRepoTotalRecordDao
	EasyRepoTotalIncreRecordDao *dao.EasyRepoTotalIncreRecordDao
	EeIncreLineDao              *dao.EeIncreLineDao

	// 展示给 ee, 后端代码
	ToEEIncreDataInfoBackend []*ToEEIncreDataInfo

	// 展示给 ee，前端代码
	ToEEIncreDataInfoFront []*ToEEIncreDataInfo

	ToEEIncreTotalCntBackend int32
	ToEEIncreTotalCntFront   int32
	ToEEIncreTotalCnt        int32

	LastRepoInfos []*RepoInfo            // 每个esay代码库最后提交的信息
	AllRepoInfos  map[string][]*RepoInfo // 昨天每个代码库每次提交的信息
	LastDayInfo   *LastDayTotalIncreInfo

	// 总量
	AllUserLineCnt int32

	AllEasyLineCnt   int32
	AllGrenerateRate string

	// 总量（不含单测）
	AllUserLineCntWithoutUnitTest int32

	AllEasyLineCntWothoutUnitTest int32

	// 不含单测的生成率
	AllGrenerateRateWithoutUnitTest string

	// 所有代码最后一次执行的增量数据
	AllIncreUserLineCnt int32

	AllIncreEasyLineCnt   int32
	AllIncreGrenerateRate string

	// 单测生成率： easy单测生成/总单测行数
	EasyUTRatioStr string

	// 单测占比: 总单测/总代码行数
	UTRatioStr string
}

type LastDayTotalIncreInfo struct {
	EasyTotalCnt     int    // easy增量汇总
	UserTotalCnt     int    // user增量汇总
	EasyTotalRateStr string // easy 增量生成率
}

type ToEEIncreDataInfo struct {
	CreateAtForPerson string
	RepoName          string
	UserName          string
	Lang              string
	CreateAt          int64
	IncreLine         int64
}

type RepoInfo struct {
	// 总量数据（含单测）
	UserTotalCnt int32

	EasyTotalCnt    int32
	GenerateRateNum float64
	GenerateRate    string

	// 总量数据 (不含单测）
	UserTotalCntWithoutUnitTest int32

	EasyTotalCntWithoutUnitTest int32
	GenerateRateWithoutUnitTest string

	IncreUserTotalCnt int32
	IncreEasyTotalCnt int32
	IncreGenerateRate string

	CreateAt          int64  // unix时间戳
	CreateAtForPerson string // 可读性更高的时间戳
	RepoName          string
	CommitUser        string
	CommitURL         string
}

func NewSendMailWorker(ctx *easy.Context) *SendMailWorker {
	return &SendMailWorker{
		Context:                     ctx,
		EasyLastRecordDao:           dao.NewEasyLastRecordDao(ctx),
		EasyRepoTotalRecordDao:      dao.NewEasyRepoTotalRecordDao(ctx),
		EasyRepoTotalIncreRecordDao: dao.NewEasyRepoTotalIncreRecordDao(ctx),
		EeIncreLineDao:              dao.NewEeIncreLineDao(ctx),
		ToEEIncreDataInfoBackend:    make([]*ToEEIncreDataInfo, 0),
		ToEEIncreDataInfoFront:      make([]*ToEEIncreDataInfo, 0),
		LastRepoInfos:               make([]*RepoInfo, 0),
		AllRepoInfos:                make(map[string][]*RepoInfo),
		LastDayInfo:                 &LastDayTotalIncreInfo{},
	}
}

func (sw *SendMailWorker) Validate() error {
	if sw.EasyLastRecordDao == nil {
		return errors.New("sw.EasyLastRecordDao is nil")
	}
	if sw.EasyRepoTotalRecordDao == nil {
		return errors.New("sw.EasyRepoTotalRecordDao is nil")
	}
	return nil
}

// 最后一次提交
func (sw *SendMailWorker) InitLastRepoInfos() error {
	lastRecords, err := sw.EasyLastRecordDao.List()
	if err != nil {
		sw.Context.SLog.Error("list the last record error").Print()
		return err
	}

	var easyUTTotalCnt int32 = 0
	var userUTTotalCnt int32 = 0

	for _, lastRecord := range lastRecords {
		repoName := lastRecord.RepoName
		createTime := lastRecord.LastTime

		totalRecordList, err := sw.EasyRepoTotalRecordDao.List(createTime)
		if err != nil {
			sw.Context.SLog.Error("sendhi worker get record failed on list").Set("create_time", createTime).Set("repo_name", repoName).Print()
			continue
		}
		if len(totalRecordList) == 0 {
			sw.Context.SLog.Error("the  total record len is 0").Set("repoName", repoName).Set("time", createTime).Print()
			continue
		}

		increRecord, err := sw.EasyRepoTotalIncreRecordDao.Get(repoName, createTime)
		if err != nil {
			sw.Context.SLog.Error("sendhi worker get record failed on get total inre record").
				Set("repo_name", repoName).Set("create_time", createTime).SetErr(err).Print()
			increRecord = &entity.EasyRepoTotalIncreRecord{}
			increRecord.UserTotalCnt = 0
			increRecord.EasyTotalCnt = 0
		}

		if increRecord == nil {
			sw.Context.SLog.Error("incre record failed. incre record == nil").
				Set("repo_name", repoName).Set("create_time", createTime).Print()
			increRecord = &entity.EasyRepoTotalIncreRecord{}
			increRecord.UserTotalCnt = 0
			increRecord.EasyTotalCnt = 0
		}

		// 获取目标数据
		var targetRecord *entity.EasyRepoTotalRecord
		for _, totalRecord := range totalRecordList {
			if totalRecord.RepoName == repoName {
				targetRecord = totalRecord
				break
			}
		}

		if targetRecord == nil {
			sw.Context.SLog.Error("the target total record is not found").Set("repoName", repoName).Set("time", createTime).Print()
			continue
		}

		repoInfo := &RepoInfo{}
		repoInfo.CommitUser = lastRecord.CommitUser
		repoInfo.CreateAt = createTime
		repoInfo.CreateAtForPerson = easyutils.UnixTimeStampToString(createTime)
		repoInfo.EasyTotalCnt = targetRecord.EasyTotalCnt
		repoInfo.UserTotalCnt = targetRecord.UserTotalCnt
		repoInfo.GenerateRateNum = float64(targetRecord.EasyTotalCnt) / float64(targetRecord.UserTotalCnt+targetRecord.EasyTotalCnt) * 100
		repoInfo.GenerateRate = fmt.Sprintf("%.2f%%", repoInfo.GenerateRateNum)
		repoInfo.RepoName = repoName
		repoInfo.CommitURL = lastRecord.CommitURL

		// 总量数据 (不含单测）
		repoInfo.UserTotalCntWithoutUnitTest = targetRecord.UserTotalCnt - targetRecord.UserUnitTestTotalCnt
		repoInfo.EasyTotalCntWithoutUnitTest = targetRecord.EasyTotalCnt - targetRecord.EasyUnitTestTotalCnt

		easyUTTotalCnt += targetRecord.EasyUnitTestTotalCnt
		userUTTotalCnt += targetRecord.UserUnitTestTotalCnt

		// nolint:lll
		repoInfo.GenerateRateWithoutUnitTest = fmt.Sprintf("%.2f%%", float64(repoInfo.EasyTotalCntWithoutUnitTest)/float64(repoInfo.UserTotalCntWithoutUnitTest+repoInfo.EasyTotalCntWithoutUnitTest)*100)
		sw.AllUserLineCntWithoutUnitTest += repoInfo.UserTotalCntWithoutUnitTest
		sw.AllEasyLineCntWothoutUnitTest += repoInfo.EasyTotalCntWithoutUnitTest

		// 增量信息
		repoInfo.IncreEasyTotalCnt = increRecord.EasyTotalCnt
		repoInfo.IncreUserTotalCnt = increRecord.UserTotalCnt
		sw.AllIncreEasyLineCnt += increRecord.EasyTotalCnt
		sw.AllIncreUserLineCnt += increRecord.UserTotalCnt

		if increRecord.EasyTotalCnt == 0 || increRecord.UserTotalCnt == 0 {
			repoInfo.IncreGenerateRate = "NONE"
		} else if increRecord.EasyTotalCnt == 0 && increRecord.UserTotalCnt == 0 {
			repoInfo.IncreGenerateRate = "NONE"
		} else {
			repoInfo.IncreGenerateRate = fmt.Sprintf("%.2f%%", float64(increRecord.EasyTotalCnt)/float64(increRecord.EasyTotalCnt+increRecord.UserTotalCnt)*100)
		}

		sw.LastRepoInfos = append(sw.LastRepoInfos, repoInfo)
		sw.AllEasyLineCnt += targetRecord.EasyTotalCnt
		sw.AllUserLineCnt += targetRecord.UserTotalCnt
	}

	sw.AllGrenerateRate = fmt.Sprintf("%.2f%%", float64(sw.AllEasyLineCnt)/float64(sw.AllEasyLineCnt+sw.AllUserLineCnt)*100)
	sort.Slice(sw.LastRepoInfos, func(i, j int) bool {
		return sw.LastRepoInfos[i].GenerateRateNum > sw.LastRepoInfos[j].GenerateRateNum
	})
	// 总量生成率
	// nolint:lll
	sw.AllGrenerateRateWithoutUnitTest = fmt.Sprintf("%.2f%%", float64(sw.AllEasyLineCntWothoutUnitTest)/float64(sw.AllUserLineCntWithoutUnitTest+sw.AllEasyLineCntWothoutUnitTest)*100)

	// 获取增量生成率（所有代码库合并起来的增量生成率）
	sw.AllIncreGrenerateRate = fmt.Sprintf("%.2f%%", float64(sw.AllIncreEasyLineCnt)/float64(sw.AllIncreEasyLineCnt+sw.AllIncreUserLineCnt)*100)

	// easy单测占比： easy单测/总单测
	sw.EasyUTRatioStr = fmt.Sprintf("%.2f%%", 100*float64(easyUTTotalCnt)/float64(easyUTTotalCnt+userUTTotalCnt))

	// 单测占比： 总单测/总代吗行数
	totalCnt := sw.AllEasyLineCnt + sw.AllUserLineCnt
	utTotalCnt := totalCnt - (sw.AllEasyLineCntWothoutUnitTest + sw.AllUserLineCntWithoutUnitTest)
	sw.UTRatioStr = fmt.Sprintf("%.2f%%", 100*float64(utTotalCnt)/float64(totalCnt))
	return nil
}

// golang
func (sw *SendMailWorker) InitToEEIncreDataInfoBackend() error {
	lastDay := time.Now().AddDate(0, 0, -1)
	lastDayStart := time.Date(lastDay.Year(), lastDay.Month(), lastDay.Day(), 0, 0, 0, 0, lastDay.Location())
	lastDayEnd := lastDayStart.Add(24 * time.Hour)
	listInfo, err := sw.EeIncreLineDao.List(lastDayStart.Unix(), lastDayEnd.Unix())
	if err != nil {
		sw.Context.SLog.Warning("sendhi worker get ee_incre line info failed on list").Set("last_day", lastDayStart).Set("last_dayEnd", lastDayEnd).Print()
		return err
	}

	for _, item := range listInfo {
		newItem := &ToEEIncreDataInfo{
			RepoName:          item.Repo,
			UserName:          item.Username,
			IncreLine:         int64(item.IncreLine),
			CreateAtForPerson: easyutils.UnixTimeStampToString(item.CreateAt),
			CreateAt:          item.CreateAt,
			Lang:              "go",
		}
		sw.ToEEIncreTotalCntBackend += item.IncreLine
		sw.ToEEIncreDataInfoBackend = append(sw.ToEEIncreDataInfoBackend, newItem)
	}
	sw.ToEEIncreTotalCnt += sw.ToEEIncreTotalCntBackend

	sort.Slice(sw.ToEEIncreDataInfoBackend, func(i, j int) bool {
		return sw.ToEEIncreDataInfoBackend[i].IncreLine > sw.ToEEIncreDataInfoBackend[j].IncreLine
	})

	return nil
}

// golang
func (sw *SendMailWorker) InitToEEIncreDataInfoFront() error {
	lastDay := time.Now().AddDate(0, 0, -1)
	lastDayStart := time.Date(lastDay.Year(), lastDay.Month(), lastDay.Day(), 0, 0, 0, 0, lastDay.Location())
	lastDayEnd := lastDayStart.Add(24 * time.Hour)
	feStatClient := remote.NewFeStatisticsClient(sw.Context)

	query := &remote.FeStatisticsGet{}
	query.St = lastDayStart.Unix()
	query.Et = lastDayEnd.Unix()

	header := &remote.FeStatisticsHeader{}
	header.ContentType = "application/json"

	result, err := feStatClient.Do(query, header)
	if err != nil {
		sw.Context.SLog.Warning("sendhi worker get fe_statistics failed on query").Set("start", lastDayStart).Set("end", lastDayEnd).SetErr(err).Print()
		return err
	}

	for _, item := range result.Data {
		newItem := &ToEEIncreDataInfo{
			RepoName:          item.Repo,
			UserName:          item.User,
			IncreLine:         int64(item.IncreLine),
			CreateAtForPerson: easyutils.UnixTimeStampToString(item.CreateAt),
			CreateAt:          item.CreateAt,
			Lang:              item.Lang,
		}
		sw.ToEEIncreTotalCntFront += int32(item.IncreLine)
		sw.ToEEIncreDataInfoFront = append(sw.ToEEIncreDataInfoFront, newItem)
	}
	sw.ToEEIncreTotalCnt += sw.ToEEIncreTotalCntFront

	sort.Slice(sw.ToEEIncreDataInfoFront, func(i, j int) bool {
		return sw.ToEEIncreDataInfoFront[i].IncreLine > sw.ToEEIncreDataInfoFront[j].IncreLine
	})

	return nil
}

func (sw *SendMailWorker) InitAllRepoInfos() error {
	lastDayRecords, err := sw.EasyRepoTotalRecordDao.ListLastDay() // 全量数据
	if err != nil {
		sw.Context.SLog.Error("list the all record error").Print()
		return err
	}

	lastDayIncreRecord, err := sw.EasyRepoTotalIncreRecordDao.ListLastDay() // 增量数据
	if err != nil {
		sw.Context.SLog.Error("list the all incre record error").SetErr(err).Print()
		return err
	}
	increRecordMap := totalIncreRecord2Map(lastDayIncreRecord)

	records := filtRecords(lastDayRecords)
	for _, record := range records {
		repoName := record.RepoName
		createTime := record.CreateAt

		repoInfo := &RepoInfo{}
		repoInfo.CreateAt = createTime
		repoInfo.CreateAtForPerson = easyutils.UnixTimeStampToString(createTime)
		repoInfo.EasyTotalCnt = record.EasyTotalCnt
		repoInfo.UserTotalCnt = record.UserTotalCnt
		repoInfo.GenerateRate = fmt.Sprintf("%.2f%%", float64(record.EasyTotalCnt)/float64(record.EasyTotalCnt+record.UserTotalCnt)*100)
		repoInfo.RepoName = repoName
		repoInfo.CommitURL = record.CommitURL

		// 总量数据 (不含单测）
		repoInfo.UserTotalCntWithoutUnitTest = record.UserTotalCnt - record.UserUnitTestTotalCnt //
		repoInfo.EasyTotalCntWithoutUnitTest = record.EasyTotalCnt - record.EasyUnitTestTotalCnt
		// nolint:lll
		repoInfo.GenerateRateWithoutUnitTest = fmt.Sprintf("%.2f%%", float64(repoInfo.UserTotalCntWithoutUnitTest)/float64(repoInfo.UserTotalCntWithoutUnitTest+repoInfo.EasyTotalCntWithoutUnitTest)*100)

		repoMap, ok := increRecordMap[repoName]
		if !ok {
			// 增量信息如果没有存到，抛出-1 以作为邮件展示
			repoInfo.IncreEasyTotalCnt = 0
			repoInfo.IncreUserTotalCnt = 0
			repoInfo.IncreGenerateRate = "NONE"
		} else {
			increInfo, ok := repoMap[createTime]
			if !ok {
				repoInfo.IncreEasyTotalCnt = 0
				repoInfo.IncreUserTotalCnt = 0
				repoInfo.IncreGenerateRate = "NONE"
				repoInfo.CommitUser = "NONE"
			} else {
				repoInfo.IncreEasyTotalCnt = increInfo.EasyCnt
				repoInfo.IncreUserTotalCnt = increInfo.UserCnt
				repoInfo.IncreGenerateRate = fmt.Sprintf("%.2f%%", float64(increInfo.EasyCnt)/float64(increInfo.EasyCnt+increInfo.UserCnt)*100)
				repoInfo.CommitUser = increInfo.CommitUser
			}
		}
		sw.AllRepoInfos[repoName] = append(sw.AllRepoInfos[repoName], repoInfo)

		// 生成增量汇总信息
		sw.LastDayInfo.EasyTotalCnt += int(repoInfo.IncreEasyTotalCnt)
		sw.LastDayInfo.UserTotalCnt += int(repoInfo.IncreUserTotalCnt)
	}
	// 生成增量的汇总的信息
	sw.LastDayInfo.EasyTotalRateStr = fmt.Sprintf("%.2f%%",
		float64(sw.LastDayInfo.EasyTotalCnt)/float64(sw.LastDayInfo.EasyTotalCnt+sw.LastDayInfo.UserTotalCnt)*100)

	sw.Context.SLog.Info("get last repo infos").Set("repo cnt", len(sw.AllRepoInfos)).Print()

	// 对每个代码库下进行排序
	for repoName, records := range sw.AllRepoInfos {
		sort.Slice(records, func(i, j int) bool {
			return records[i].CreateAt < records[j].CreateAt
		})
		sw.AllRepoInfos[repoName] = records
	}

	return nil
}

type IncreInfo struct {
	UserCnt    int32
	EasyCnt    int32
	CommitUser string
}

func totalIncreRecord2Map(increRecords []*entity.EasyRepoTotalIncreRecord) map[string]map[int64]*IncreInfo {
	res := make(map[string]map[int64]*IncreInfo)
	for _, record := range increRecords {
		repoName := record.RepoName
		createAt := record.CreateAt
		increInfo := &IncreInfo{
			UserCnt:    record.UserTotalCnt,
			EasyCnt:    record.EasyTotalCnt,
			CommitUser: record.CommitUser,
		}

		innerMap, ok := res[repoName]
		if !ok {
			innerMap = make(map[int64]*IncreInfo)
		}
		innerMap[createAt] = increInfo
		res[repoName] = innerMap
	}
	return res
}

func (sw *SendMailWorker) SendToMail() error {
	t, err := template.New("easy-repo-statistics").Funcs(template.FuncMap{
		"inc": func(i int) int {
			return i + 1
		},
	}).Parse(easyRepoTmpl)
	if err != nil {
		sw.Context.SLog.Error("easy-repo-statistics template parse failed").SetErr(err).Print()
		return err
	}

	var buffer bytes.Buffer
	if err := t.Execute(&buffer, sw); err != nil {
		sw.Context.SLog.Error("easy-repo-statistics template execute failed").SetErr(err).Print()
		return err
	}

	sw.Context.SLog.Info("send mail is running...").Print()

	subject := "easy代码库行数度量统计"
	msg := buffer.String()

	if err := smtp.SendMail(
		"mail2-in.baidu.com:25",                  // 内网邮件服务器
		nil,                                      // 内网邮件服务器支持匿名SMTP
		conf.Email.EasyCodemetricEmailPersonFrom, // 发信人
		conf.Email.EasyCodemetricEmailPersonTo,
		// MailEasyTo,              // 收信人
		[]byte(strings.Join([]string{
			"FROM: <EMAIL>", // 邮件客户端显示的发信人
			"TO: <EMAIL>",   // 邮件客户端显示的收信人
			"SUBJECT: " + subject,          // 标题
			"Content-Type: text/html; charset=UTF-8;",
			"",  // 邮件头和正文之间要有一个空行
			msg, // 正文
		}, "\r\n")), // RFC协议规定的换行符是\r\n
	); err != nil {
		sw.Context.SLog.Error("send mail fail").SetErr(err).Print()
		return err
	}

	sw.Context.SLog.Info("send mail success").Print()

	return nil
}

func filtRecords(records []*entity.EasyRepoTotalRecord) []*entity.EasyRepoTotalRecord {
	recordMap := map[string]*entity.EasyRepoTotalRecord{}
	for _, record := range records {
		uniqueKey := genCommitUniqueKey(record.RepoName, record.CommitURL)
		if preRecord, ok := recordMap[uniqueKey]; !ok {
			recordMap[uniqueKey] = record
		} else {
			// 比较哪个时间早
			if record.CreateAt < preRecord.CreateAt {
				recordMap[uniqueKey] = record
			}
		}
	}

	var res []*entity.EasyRepoTotalRecord
	for _, record := range recordMap {
		res = append(res, record)
	}
	return res
}

func genCommitUniqueKey(repo string, commitURL string) string {
	return repo + "-" + commitURL
}

/*
前面是一个总的汇总信息
有多少个代码库有变更，以最后一次提交为准的数据，多少行是生成的，多少行是业务自己写的，生成率是多少

每个代码库，每次提交，时间点，生成行数，自己写的行数，生成率
*/
const easyRepoTmpl = `
<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
	</head>
	<body>

	历史代码库最后一次检测数据统计
	<table border="1">
		<tr>
			<th>总Easy代码行数</th>
			<th>总User代码行数</th>
			<th>总代码生成率</th>
			<th>总Easy代码行数(不含单测)</th>
			<th>总User代码行数(不含单测)</th>
			<th>总代码生成率(不含单测)</th>
			<th>总Easy增量代码行数(各代码库最后一次, 下同）</th>
			<th>总User增量代码行数 </th>
			<th>总Easy增量代码生成率</th>
			<th>easy单测占总单测比例</th>
			<th>单测占总代码比例</th>
	  	<tr>
		<tr>
			<td>{{ .AllEasyLineCnt}}</td>
			<td>{{ .AllUserLineCnt}}</td>
			<td>{{ .AllGrenerateRate}}</td>
			<td>{{ .AllEasyLineCntWothoutUnitTest}}</td>
			<td>{{ .AllUserLineCntWithoutUnitTest}}</td>
			<td>{{ .AllGrenerateRateWithoutUnitTest}}</td>
			<td>{{ .AllIncreEasyLineCnt}}</td>
			<td>{{ .AllIncreUserLineCnt}}</td>
			<td>{{ .AllIncreGrenerateRate}}</td>
			<td>{{ .EasyUTRatioStr}}</td>
			<td>{{ .UTRatioStr}}</td>
		<tr>
	</table>
	<br />
	<br />


	所有代码库历史检测记录
	<table border="1">
		<tr>
			<th>序号</th>
			<th>代码库名称</th>
			<th>最后一次提交时间</th>
	  		<th>easy生成行数</th>
			<th>user业务行数</th>	
			<th>代码生成率</th>
			<th>easy生成行数(不含单测)</th>
			<th>user业务行数(不含单测)</th>	
			<th>代码生成率(不含单测)</th>
			<th>使用人</th>
			<th>commit url</th>
		</tr>
		{{ range $index, $info := .LastRepoInfos }}
			<td> {{$index | inc}}</td>
			<td>{{$info.RepoName}}</td>
			<td>{{$info.CreateAtForPerson}}</td>
			<td>{{$info.EasyTotalCnt}}</td>
			<td>{{$info.UserTotalCnt}}</td>
			<td>{{$info.GenerateRate}}</td>
			<td>{{$info.EasyTotalCntWithoutUnitTest}}</td>
			<td>{{$info.UserTotalCntWithoutUnitTest}}</td>
			<td>{{$info.GenerateRateWithoutUnitTest}}</td>
			<td>{{$info.CommitUser}}</td>
			<td>{{$info.CommitURL}}</td>
		</tr>
		{{end}}	
  	</table>

	<br />
	<br />
	  
	*****************************************************
	<br />
	<br />
	昨天所有检测记录汇总
	<table border="1">
		<tr>
			<th>easy生成代码汇总</th>
			<th>user业务代码汇总</th>
			<th>easy增量代码生成率</th>
		</tr>
		<tr>
			<td>{{ .LastDayInfo.EasyTotalCnt}}</td>
			<td>{{ .LastDayInfo.UserTotalCnt}}</td>
			<td>{{ .LastDayInfo.EasyTotalRateStr}}</td>
		<tr>		
	</table>	

	
	<br />
	<br />
	昨天所有检测记录
	
	<table border="1">
		<tr>
			<th>序号</th>
			<th>代码库名称</th>
			<th>提交时间</th>
			<th>easy生成代码行数</th>
			<th>user业务代码行数</th>	
			<th>代码生成率</th>
			<th>增量easy生成代码行数</th>
			<th>增量user业务代码行数</th>
			<th>增量代码生成率</th>
			<th>使用人</th>
			<th>commit url</th>
		</tr>
		
		{{ $index := 0}}
		{{range  $infos := .AllRepoInfos}}
			{{range  $infos}}
				{{ $index = inc $index }}
			<tr>
				<td>{{$index}}</td>
				<td>{{.RepoName}}</td>
				<td>{{.CreateAtForPerson}}</td>
				<td>{{.EasyTotalCnt}}</td>
				<td>{{.UserTotalCnt}}</td>
				<td>{{.GenerateRate}}</td>
				<td>{{.IncreEasyTotalCnt}}</td>
				<td>{{.IncreUserTotalCnt}}</td>
				<td>{{.IncreGenerateRate}}</td>
				<td>{{.CommitUser}}</td>
				<td>{{.CommitURL}}</td>
			</tr>
			{{end}}
		{{end}}	
	</table>	

	<br />
	<br />
	*****************************************************
	<br />
	<br />
	对EE侧数据统计 easy 生成(昨天)	
	
	easy模块
	<table border="1">
		<tr>
			<th>序号</th>
			<th>代码库名称</th>
			<th>提交时间</th>
			<th>增量行数</th>
			<th>语言类型</th>
			<th>使用人</th>
		</tr>	

		{{ $index := 0}}
		{{ range $index, $info := .ToEEIncreDataInfoBackend }}
			<tr>
				<td> {{$index | inc}}</td>
				<td>{{$info.RepoName}}</td>
				<td>{{$info.CreateAtForPerson}}</td>
				<td>{{$info.IncreLine}}</td>
				<td>{{$info.Lang}}</td>
				<td>{{$info.UserName}}</td>
			</tr>
		{{end}}	
	</table>	

	<br />
	<br />

	前端模块
	<table border="1">
	<tr>
		<th>序号</th>
		<th>代码库名称</th>
		<th>提交时间</th>
		<th>增量行数</th>
		<th>语言类型</th>
		<th>使用人</th>
	</tr>	

	{{ $index := 0}}
	{{ range $index, $info := .ToEEIncreDataInfoFront }}
		<tr>
			<td> {{$index | inc}}</td>
			<td>{{$info.RepoName}}</td>
			<td>{{$info.CreateAtForPerson}}</td>
			<td>{{$info.IncreLine}}</td>
			<td>{{$info.Lang}}</td>
			<td>{{$info.UserName}}</td>
		</tr>
	{{end}}	
	</table>

	<br />
	<br />
	  
	<table border="1">
		<tr>
			<th>对EE侧golang模块总增量行数(昨天）</th>
			<th>对EE侧前端模块总增量行数(昨天)</th>
			<th>对EE侧总增量行数((昨天)</th>
	  	<tr>
		<tr>
			<td>{{ .ToEEIncreTotalCntBackend}}</td>
			<td>{{ .ToEEIncreTotalCntFront}}</td>
			<td>{{ .ToEEIncreTotalCnt}}</td>
		<tr>
	</table>

	<br />
	<br />

	</body>
</html>
`
