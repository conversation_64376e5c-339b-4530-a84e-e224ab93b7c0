package worker

import (
	"bytes"
	"fmt"
	"html/template"
	"net/smtp"
	"sort"
	"strings"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
)

func (s *DumpScheduler) Handler() {
	ctx := easy.NewContext()

	ctx.SLog.Info("dumpSchedule handler start...").Print()

	worker := NewDumpMailWorker(ctx)
	if err := worker.SendMailTask(); err != nil {
		ctx.SLog.Warning("dumpSchedule worker send mail task error").Print()
	}
	ctx.SLog.Info("dumpSchedule handler end").Print()
}

type DumpMailWorker struct {
	Context    *easy.Context
	defaultDao *dao.TangramReplayDefaultStatisticsDao
	usageDao   *dao.TangramReplayUsageStatisticsDao

	DefaultTableInfos []*TableInfo
	UsageTableInfos   []*TableInfo

	AvgDefault *AverageDefaultTableInfo
	AvgUsage   *AverageUsageTableInfo
}

// 两个表结构一致，需要上传报表的数据一致
type TableInfo struct {
	APPName         string
	User            string
	SuccessCnt      int32
	FailCnt         int32
	TotalCnt        int32
	SuccessRatio    float64
	SuccessRatioStr string
	CoverageRate    float64
	CoverageRateStr string
	Timestamp       string
}

// 昨天数据：usage表
type AverageUsageTableInfo struct {
	AvgCoverageRatioStr string // 昨天平均覆盖率
	AvgSuccessRatioStr  string // 昨天平均成功率
	UserCnt             int    // 昨天使用 rd 数量
	RepoCnt             int    // 昨天代码库覆盖数
}

// APP维度最后一次执行数据: default 表
type AverageDefaultTableInfo struct {
	AvgCoverageRatioStr string // 每APP最后一次执行的平均覆盖率
	AvgSuccessRatioStr  string // 每APP最后一次执行的平均成功率
	RepoCnt             int    //
}

func NewDumpMailWorker(ctx *easy.Context) *DumpMailWorker {
	return &DumpMailWorker{
		Context:    ctx,
		defaultDao: dao.NewTangramReplayDefaultStatisticsDao(ctx),
		usageDao:   dao.NewTangramReplayUsageStatisticsDao(ctx),
	}
}

func (w *DumpMailWorker) SendMailTask() error {
	if err := w.initData(); err != nil {
		w.Context.SLog.Warning("DumpMailWorker initData failed").SetErr(err).Print()
		return err
	}

	t, err := template.New("dump").Funcs(template.FuncMap{
		"inc": func(i int) int {
			return i + 1
		},
	}).Parse(dumpTableTmpl)
	if err != nil {
		w.Context.SLog.Error("dump template parse failed").SetErr(err).Print()
		return err
	}

	var buffer bytes.Buffer
	if err := t.Execute(&buffer, w); err != nil {
		w.Context.SLog.Error("dump template execute failed").SetErr(err).Print()
		return err
	}

	w.Context.SLog.Info("send mail for dump is running...").Print()

	subject := "tangram dump数据报表"
	msg := buffer.String()

	if err := smtp.SendMail(
		"mail2-in.baidu.com:25",               // 内网邮件服务器
		nil,                                   // 内网邮件服务器支持匿名SMTP
		conf.Email.TangramDumpEmailPersonFrom, // 发信人
		conf.Email.TangramDumpEmailPersonTo,   // 收信人
		[]byte(strings.Join([]string{
			"FROM: <EMAIL>", // 邮件客户端显示的发信人
			"TO: <EMAIL>",   // 邮件客户端显示的收信人
			"SUBJECT: " + subject,          // 标题
			"Content-Type: text/html; charset=UTF-8;",
			"",  // 邮件头和正文之间要有一个空行
			msg, // 正文
		}, "\r\n")), // RFC协议规定的换行符是\r\n
	); err != nil {
		w.Context.SLog.Error("send mail for dump fail").SetErr(err).Print()
		return err
	}

	w.Context.SLog.Info("send mail for dump success").Print()
	return nil
}

// 获取数据
func (w *DumpMailWorker) initData() error {
	DefaultTableInfos, err := w.getDefaultTableLastData()
	if err != nil {
		w.Context.SLog.Warning("DumpMailWorker getDefaultTableLastData fail").SetErr(err).Print()
		return err
	}
	w.DefaultTableInfos = DefaultTableInfos

	UsageTableInfos, err := w.getUsageTableLastData()
	if err != nil {
		w.Context.SLog.Warning("DumpMailWorker getUsageTableLastData fail").SetErr(err).Print()
		return err
	}
	w.UsageTableInfos = UsageTableInfos

	AvgDefaultTableInfo := w.getAverageDefaultTableInfo()
	AvgUsageTableInfo := w.getAverageUsageTableInfo()
	w.AvgDefault = AvgDefaultTableInfo
	w.AvgUsage = AvgUsageTableInfo

	return nil
}

func (w *DumpMailWorker) getDefaultTableLastData() ([]*TableInfo, error) {
	list, err := w.defaultDao.ListAPPDataLastTime()
	if err != nil {
		return nil, err
	}

	var res []*TableInfo
	for _, item := range list {
		entry := &TableInfo{}
		entry.APPName = item.App
		entry.User = item.User
		entry.TotalCnt = item.ReplayCnt
		entry.SuccessCnt = item.ReplaySuccessCnt
		entry.FailCnt = item.ReplayFailedCnt
		// 如果 totalcnt = 0, 后续的平均覆盖率和平均成功率排除该项数据
		if entry.TotalCnt != 0 {
			entry.SuccessRatio = 100 * float64(entry.SuccessCnt) / float64(entry.TotalCnt)
			entry.SuccessRatioStr = fmt.Sprintf("%.1f%%", entry.SuccessRatio)

			entry.CoverageRate = float64(item.CoverageRate)
			entry.CoverageRateStr = fmt.Sprintf("%.1f%%", entry.CoverageRate)
		} else {
			entry.SuccessRatio = -1        // -1 用于在计算平均值时过滤
			entry.SuccessRatioStr = "100%" // 这个值用于展示

			entry.CoverageRate = -1        // float64(item.CoverageRate)
			entry.CoverageRateStr = "100%" //
		}

		entry.Timestamp = item.UseTime

		res = append(res, entry)
	}

	sort.Slice(res, func(i, j int) bool {
		return res[i].SuccessRatio > res[j].SuccessRatio
	})

	return res, nil
}

func (w *DumpMailWorker) getUsageTableLastData() ([]*TableInfo, error) {
	list, err := w.usageDao.ListAPPLastDayData()
	if err != nil {
		return nil, err
	}

	var res []*TableInfo

	for _, item := range list {
		entry := &TableInfo{}
		entry.APPName = item.App
		entry.User = item.User
		entry.TotalCnt = item.ReplayCnt
		entry.SuccessCnt = item.ReplaySuccessCnt
		entry.FailCnt = item.ReplayFailedCnt

		// 如果 totalcnt = 0, 后续的平均覆盖率和平均成功率排除该项数据
		if entry.TotalCnt != 0 {
			entry.SuccessRatio = 100 * float64(entry.SuccessCnt) / float64(entry.TotalCnt)
			entry.SuccessRatioStr = fmt.Sprintf("%.1f%%", entry.SuccessRatio)

			entry.CoverageRate = float64(item.CoverageRate)
			entry.CoverageRateStr = fmt.Sprintf("%.1f%%", entry.CoverageRate)
		} else {
			entry.SuccessRatio = -1      // -1 用于在计算平均值时过滤
			entry.SuccessRatioStr = "0%" // 这个值用于展示

			entry.CoverageRate = -1      // float64(item.CoverageRate)
			entry.CoverageRateStr = "0%" //
		}

		entry.Timestamp = item.UseTime

		res = append(res, entry)
	}

	sort.Slice(res, func(i, j int) bool {
		return res[i].SuccessRatio > res[j].SuccessRatio
	})

	return res, nil
}

func (w *DumpMailWorker) getAverageUsageTableInfo() *AverageUsageTableInfo {
	res := &AverageUsageTableInfo{}
	appMap := make(map[string]struct{})
	userMap := make(map[string]struct{})

	var avgCoverRatio float64 = 0

	zeroCaseRepoCnt := 0
	// 成功率按照case数来算；覆盖率后续计算两个：总成功case数 / 总case数和 代码库的算数平均
	successCaseCnt := 0
	totalCaseCnt := 0
	for _, item := range w.UsageTableInfos {
		appMap[item.APPName] = struct{}{}
		userMap[item.User] = struct{}{}
		if item.TotalCnt == 0 {
			zeroCaseRepoCnt++
		} else {
			// / 平均覆盖率
			avgCoverRatio += item.CoverageRate
			// 平均成功率
			successCaseCnt += int(item.SuccessCnt)
			totalCaseCnt += int(item.TotalCnt)
		}
	}
	lenCnt := len(w.UsageTableInfos) - zeroCaseRepoCnt // 减去 0 case数

	res.RepoCnt = len(appMap) - zeroCaseRepoCnt // 减去 0 case数
	res.UserCnt = len(userMap)

	if lenCnt == 0 {
		res.AvgCoverageRatioStr = "0.0%"
		res.AvgSuccessRatioStr = "0.0%"
	} else {
		res.AvgCoverageRatioStr = fmt.Sprintf("%.1f%%", avgCoverRatio/float64(lenCnt))
		res.AvgSuccessRatioStr = fmt.Sprintf("%.1f%%", 100*float64(successCaseCnt)/float64(totalCaseCnt))
	}
	return res
}

func (w *DumpMailWorker) getAverageDefaultTableInfo() *AverageDefaultTableInfo {
	res := &AverageDefaultTableInfo{}
	appMap := make(map[string]struct{})

	var avgCoverRatio float64 = 0

	zeroCaseRepoCnt := 0
	// 成功率按照case数来算；覆盖率后续计算两个：总成功case数 / 总case数和 代码库的算数平均
	successCaseCnt := 0
	totalCaseCnt := 0
	for _, item := range w.DefaultTableInfos {
		if item.TotalCnt == 0 {
			zeroCaseRepoCnt++
		} else {
			// / 平均覆盖率
			avgCoverRatio += item.CoverageRate
			// 平均成功率
			successCaseCnt += int(item.SuccessCnt)
			totalCaseCnt += int(item.TotalCnt)
		}
		appMap[item.APPName] = struct{}{}
	}
	lenCnt := len(w.DefaultTableInfos) - zeroCaseRepoCnt

	res.RepoCnt = len(appMap)
	if lenCnt == 0 {
		res.AvgCoverageRatioStr = "0.0%"
		res.AvgSuccessRatioStr = "0.0%"
	} else {
		res.AvgCoverageRatioStr = fmt.Sprintf("%.1f%%", avgCoverRatio/float64(lenCnt))
		res.AvgSuccessRatioStr = fmt.Sprintf("%.1f%%", 100*float64(successCaseCnt)/float64(totalCaseCnt))
	}

	return res
}

// default表，组内跑dump, 只记录每个APP的最后一次执行数据
const dumpTableTmpl = `
<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
	</head>
	<body>
	dump历史检测记录(default表, 每个APP仅记录最后执行时间的结果)
	<table border="1">
		<tr>
			<th>序号</th>
			<th>APP</th>
			<th>使用人</th>
	  		<th>成功case数</th>
			<th>失败case数</th>	
			<th>总case数</th>
			<th>成功率</th>
			<th>覆盖率</th>
			<th>执行时间</th>
		</tr>
		{{ range $index, $info := .DefaultTableInfos }}
			<td>{{$index | inc}}</td>
			<td>{{$info.APPName}}</td>
			<td>{{$info.User}}</td>
			<td>{{$info.SuccessCnt}}</td>
			<td>{{$info.FailCnt}}</td>
			<td>{{$info.TotalCnt}}</td>
			<td>{{$info.SuccessRatioStr}}</td>
			<td>{{$info.CoverageRateStr}}</td>
			<td>{{$info.Timestamp}}</td>
		</tr>
		{{end}}	
  	</table>
	注：本次重放总case数为需要重放总case数，即: 成功case数 + 失败case数

	<br />
	<br />
	
	dump 历史平均数据
	<table border="1">
	<tr>
		<th>平均覆盖率</th>
		<th>平均成功率</th>
		<th>覆盖代码库数</th>
	</tr>
		<td>{{.AvgDefault.AvgCoverageRatioStr}}</td>
		<td>{{.AvgDefault.AvgSuccessRatioStr}}</td>
		<td>{{.AvgDefault.RepoCnt}}</td>
	</tr>
  	</table>
	注: 1)平均数只取每代码库default表最后一次执行的平均数; 2)覆盖代码库数目去除了 case数为 0 的模块
		
	
	<br />
	<br />
	*****************************************************
	<br />
	<br />
	dump昨天所有检测记录(usage表)
	<table border="1">
		<tr>
			<th>序号</th>
			<th>APP</th>
			<th>使用人</th>
	  		<th>成功case数</th>
			<th>失败case数</th>	
			<th>总case数</th>
			<th>成功率</th>
			<th>覆盖率</th>
			<th>执行时间</th>
		</tr>
		{{ range $index, $info := .UsageTableInfos }}
			<td>{{$index | inc}}</td>
			<td>{{$info.APPName}}</td>
			<td>{{$info.User}}</td>
			<td>{{$info.SuccessCnt}}</td>
			<td>{{$info.FailCnt}}</td>
			<td>{{$info.TotalCnt}}</td>
			<td>{{$info.SuccessRatioStr}}</td>
			<td>{{$info.CoverageRateStr}}</td>
			<td>{{$info.Timestamp}}</td>
		</tr>
		{{end}}	
  	</table>

	<br />
	<br />
	昨天数据
	<table border="1">
		<tr>
			<th>平均覆盖率</th>
			<th>平均成功率</th>
			<th>rd 使用人数</th>
	  		<th>覆盖代码库数</th>
		</tr>
			<td>{{.AvgUsage.AvgCoverageRatioStr}}</td>
			<td>{{.AvgUsage.AvgSuccessRatioStr}}</td>
			<td>{{.AvgUsage.UserCnt}}</td>
			<td>{{.AvgUsage.RepoCnt}}</td>
		</tr>
  	</table>
	</body>
</html>
`
