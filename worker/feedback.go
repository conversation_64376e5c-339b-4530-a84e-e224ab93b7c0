package worker

import (
	"fmt"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/sqldata"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/baseutil"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/sqldata"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// 定时任务处理的handler
func (s *FeedbackScheduler) Handler() {
	ctx := easy.NewContext()
	ctx.SLog.Info("feedback handler start...").Print()
	if err := s.handleSQLDiffFeedback(ctx); err != nil {
		ctx.SLog.Warning("handleSQLDiffFeedback error").SetErr(err).Print()

	}

}

func (s *FeedbackScheduler) handleSQLDiffFeedback(ctx *easy.Context) error {
	sqlDataStatService := service.NewStatService(ctx)
	lastday := time.Now().Add(-24 * time.Hour).Format("2006-01-02")
	reqDto := &sqldata.StatReqDto{
		Start: lastday,
		End:   lastday,
	}

	resDto, err := sqlDataStatService.StatExec(reqDto)
	if err != nil {
		ctx.SLog.Warning("stat exec error").Set("reqDto", reqDto).Print()
		return err
	}

	statResDto, ok := resDto.(*sqldata.StatResDto)
	if !ok {
		ctx.SLog.Warning("stat exec type assertion error").Set("resDto", resDto).Print()
		return err
	}

	msg := s.buildHiMsg(lastday, lastday, statResDto)
	ctx.SLog.Info("sql diff feedback").Set("start", lastday).Set("end", lastday).Set("msg", msg).Print()
	if len(msg) > 1800 {
		msg = msg[:1800]
	}

	option := baseutil.SendHiOption{
		Type:    baseutil.SendHiTypeText,
		Content: msg,
	}

	if err := baseutil.SendHi(conf.Cardsend.FeedbackWorker.URL, option); err != nil {
		ctx.SLog.Warning("send hi error").Set("url", conf.Cardsend.FeedbackWorker.URL).Print()
	}

	return nil

}

func (s *FeedbackScheduler) buildHiMsg(start string, end string, data *sqldata.StatResDto) string {
	msg := ""
	msg += fmt.Sprintf("Machine: %v\n", baseutil.GetMachine())
	msg += fmt.Sprintf("Start: %v\n", start)
	msg += fmt.Sprintf("End: %v\n\n", end)

	for _, item := range data.Data {
		msg += fmt.Sprintf("URL; %v\n总数: %v\n待review数: %v, 有问题数: %v, 无问题数: %v\n\n",
			item.URL, item.Total, item.Pending, item.HasIssue, item.NoIssue)
	}

	return msg
}

func (s *FeedbackScheduler) handleNilawayFeedback() error {
	return nil
}
