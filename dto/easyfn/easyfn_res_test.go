package easyfn

import (
	"testing"
)

// GetResDto MarshalJSON测试入口返回结果封装结构
func TestGetResDtoMarshalJSON(t *testing.T) {
	// 创建一个 GetResDto 对象进行测试
	apiInfo := &GetResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// BranchqueryResDto MarshalJSON测试入口返回结果封装结构
func TestBranchqueryResDtoMarshalJSON(t *testing.T) {
	// 创建一个 BranchqueryResDto 对象进行测试
	apiInfo := &BranchqueryResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}
