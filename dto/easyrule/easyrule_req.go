/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口入参封装dto
 * 结构体中的tag描述（由平台配置，无需手动修改）：
 * validate: 验证规则，多个规则由逗号分割
 * len：字符串限制长度
 * verify：加入参数验签 通过sign算法，框架自动验证
 * type:"raw" 标识json 格式的参数 通过raw字符串序列化
 */
package easyrule

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

type AddReqDto struct {
	easy.RequestDto

	BodyDto *AddBody `json:"-" type:"raw" validate:""`
}

type ListReqDto struct {
	easy.RequestDto
	Start  int64  `json:"start" validate:"positiveInteger,ltefield=end,required" verify:"false"`
	End    int64  `json:"end" validate:"positiveInteger,required" verify:"false"`
	Person string `json:"person" validate:"" verify:"false"`
}

type AddBodyInfoDiagnostics struct {
	Posn     string `json:"posn" validate:"required"`
	Message  string `json:"message" validate:"required"`
	Analyzer string `json:"analyzer" validate:"required"`
	URL      string `json:"url" validate:"required"`
}

type AddBodyInfo struct {
	Filename    string                    `json:"filename" validate:"required"`
	Diagnostics []*AddBodyInfoDiagnostics `json:"diagnostics" validate:"required"`
}

type AddBody struct {
	Username    string         `json:"username" validate:"required"`
	ProjectName string         `json:"project_name" validate:"required"`
	CreateAt    int64          `json:"create_at" validate:"required"`
	Info        []*AddBodyInfo `json:"info" validate:"required"`
}

func (r *AddReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}
