/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package metric

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type AddResDto struct {
	easy.BaseDto
}

func (t *AddResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type IncreResDtoData struct {
	easy.BaseDto
	Repo      string `json:"repo"`
	User      string `json:"user"`
	IncreLine int32  `json:"incre_line"`
	CreateAt  string `json:"create_at"`
	Lang      string `json:"lang"`
}

func (t *IncreResDtoData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type IncreResDto struct {
	easy.BaseDto
	Data                 []*IncreResDtoData `json:"data"`
	BackendIncreTotalCnt int32              `json:"backend_incre_total_cnt"`
	FrontIncreTotalCnt   int32              `json:"front_incre_total_cnt"`
	IncreTotalCnt        int32              `json:"incre_total_cnt"`
}

func (t *IncreResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
