package metric

import (
	"testing"
)

// IncreResDtoData MarshalJSON测试入口返回结果封装结构
func TestIncreResDtoDataMarshalJSON(t *testing.T) {
	// 创建一个 IncreResDtoData 对象进行测试
	apiInfo := &IncreResDtoData{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// IncreResDto MarshalJSON测试入口返回结果封装结构
func TestIncreResDtoMarshalJSON(t *testing.T) {
	// 创建一个 IncreResDto 对象进行测试
	apiInfo := &IncreResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.<PERSON>al<PERSON>("MarshalJSON returned an error: %v", err)
	}
	t.<PERSON>g<PERSON>("JSON : %s", jsonData)
}
