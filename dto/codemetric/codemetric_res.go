/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package codemetric

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type AddResDto struct {
	easy.BaseDto
}

func (t *AddResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type GetResDto struct {
	easy.BaseDto
	RepoName     string `json:"repo_name"`
	CreateAt     int64  `json:"create_at"`
	EasyTotalCnt int32  `json:"easy_total_cnt"`
	UserTotalCnt int32  `json:"user_total_cnt"`
}

func (t *GetResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type FileexistResDto struct {
	easy.BaseDto
	RepoName string `json:"repo_name"`
	FileName string `json:"file_name"`
	Exist    bool   `json:"exist"`
}

func (t *FileexistResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type IncreResDtoData struct {
	easy.BaseDto
	RepoName     string  `json:"repo_name"`
	CreateAt     int64   `json:"create_at"`
	EasyTotalCnt int32   `json:"easy_total_cnt"`
	UserTotalCnt int32   `json:"user_total_cnt"`
	CommitID     string  `json:"commit_id"`
	CommitUser   string  `json:"commit_user"`
	BaseCommitID string  `json:"base_commit_id"`
	Language     string  `json:"language"`
	CommitURL    string  `json:"commit_url"`
	Ratio        float64 `json:"ratio"`
}

func (t *IncreResDtoData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type IncreResDto struct {
	easy.BaseDto
	Data     []*IncreResDtoData `json:"data"`
	TotalCnt int                `json:"total_cnt"`
}

func (t *IncreResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListallResDtoData struct {
	easy.BaseDto
	RepoName       string `json:"repo_name"`
	CommitID       string `json:"commit_id"`
	CommitUser     string `json:"commit_user"`
	EasyCodeCnt    int32  `json:"easy_code_cnt"`
	UserCodeCnt    int32  `json:"user_code_cnt"`
	LastCommitTime int64  `json:"last_commit_time"`
}

func (t *ListallResDtoData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListallResDto struct {
	easy.BaseDto
	Data []*ListallResDtoData `json:"data"`
}

func (t *ListallResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type AddhalffileResDto struct {
	easy.BaseDto
	Errno  string `json:"errno"`
	ErrMsg string `json:"err_msg"`
}

func (t *AddhalffileResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type IncrebyrepoResDto struct {
	easy.BaseDto
	StatData map[string]interface{} `json:"stat_data"`
}

func (t *IncrebyrepoResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
