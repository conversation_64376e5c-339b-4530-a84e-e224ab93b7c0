/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口入参封装dto
 * 结构体中的tag描述（由平台配置，无需手动修改）：
 * validate: 验证规则，多个规则由逗号分割
 * len：字符串限制长度
 * verify：加入参数验签 通过sign算法，框架自动验证
 * type:"raw" 标识json 格式的参数 通过raw字符串序列化
 */
package nilaway

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

type ListByRepoReqDto struct {
	easy.RequestDto
	RepoName       string `json:"repo_name" validate:"" verify:"false"`
	IncludeIgnored bool   `json:"include_ignored" validate:"" verify:"false"`
}

type IgnoreBatchReqDto struct {
	easy.RequestDto
	BatchUID string `json:"batchUID" validate:"" verify:"false"`
	Repo     string `json:"repo" validate:"" verify:"false"`
}

type IgnoreOneReqDto struct {
	easy.RequestDto
	RepoName    string `json:"repoName" validate:"" verify:"false"`
	Fingerprint string `json:"fingerprint" validate:"" verify:"false"`
}

type SaveListReqDto struct {
	easy.RequestDto

	BodyDto *SaveListBody `json:"-" type:"raw" validate:""`
}

type ShowReqDto struct {
	easy.RequestDto
	CommitURL string `json:"commit_url" validate:"" verify:"false"`
	SendUser  string `json:"send_user" validate:"" verify:"false"`
}

type ProcessReqDto struct {
	easy.RequestDto
	ProcessUser   string `json:"process_user" validate:"" verify:"false"`
	IssueID       string `json:"issue_id" validate:"" verify:"false"`
	ProcessStatus string `json:"process_status" validate:"" verify:"false"`
	ProcessReason string `json:"process_reason" validate:"" verify:"false"`
}

type SaveListBodyIssues struct {
	RelFilePath string `json:"rel_file_path" validate:""`
	Fingerprint string `json:"fingerprint" validate:""`
	Message     string `json:"message" validate:""`
	Line        int    `json:"line" validate:""`
	Col         int    `json:"col" validate:""`
	CodeContext string `json:"code_context" validate:""`
	Username    string `json:"username" validate:""`
}

type SaveListBody struct {
	RepoName   string                `json:"repo_name" validate:""`
	BatchUID   string                `json:"batch_uid" validate:""`
	ScanTime   string                `json:"scan_time" validate:""`
	Issues     []*SaveListBodyIssues `json:"issues" validate:""`
	CommitURL  string                `json:"commit_url" validate:""`
	CommitUser string                `json:"commit_user" validate:""`
}

func (r *SaveListReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}
