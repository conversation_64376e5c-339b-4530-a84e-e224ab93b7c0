/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package sa

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type QueryResDto struct {
	easy.BaseDto
	Icode string `json:"icode"`
	App   string `json:"app"`
	Cnt   int32  `json:"cnt"`
	Rule  string `json:"rule"`
	Data  string `json:"data"`
}

func (t *QueryResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListIcodeResDtoList struct {
	easy.BaseDto
	App   string `json:"app"`
	Icode string `json:"icode"`
}

func (t *ListIcodeResDtoList) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListIcodeResDto struct {
	easy.BaseDto
	List []*ListIcodeResDtoList `json:"list"`
}

func (t *ListIcodeResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
