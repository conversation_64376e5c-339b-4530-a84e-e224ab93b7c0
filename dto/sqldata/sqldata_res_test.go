package sqldata

import (
	"testing"
)

// StatResDtoData MarshalJSON测试入口返回结果封装结构
func TestStatResDtoDataMarshalJSON(t *testing.T) {
	// 创建一个 StatResDtoData 对象进行测试
	apiInfo := &StatResDtoData{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// StatResDto MarshalJSON测试入口返回结果封装结构
func TestStatResDtoMarshalJSON(t *testing.T) {
	// 创建一个 StatResDto 对象进行测试
	apiInfo := &StatResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.<PERSON>al<PERSON>("MarshalJSON returned an error: %v", err)
	}
	t.<PERSON>g<PERSON>("JSON : %s", jsonData)
}
