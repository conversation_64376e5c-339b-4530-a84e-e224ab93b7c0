/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口入参封装dto
 * 结构体中的tag描述（由平台配置，无需手动修改）：
 * validate: 验证规则，多个规则由逗号分割
 * len：字符串限制长度
 * verify：加入参数验签 通过sign算法，框架自动验证
 * type:"raw" 标识json 格式的参数 通过raw字符串序列化
 */
package sqldata

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

type UploadReqDto struct {
	easy.RequestDto

	BodyDto *UploadBody `json:"-" type:"raw" validate:""`
}

type DiffviewReqDto struct {
	easy.RequestDto
	CommitURL string `json:"commit_url" validate:"" verify:"false"`
	SendUser  string `json:"send_user" validate:"" verify:"false"`
}

type ReviewReqDto struct {
	easy.RequestDto
	Reviewer        string `json:"reviewer" validate:"" verify:"false"`
	DiffID          int64  `json:"diff_id" validate:"" verify:"false"`
	ReviewStatus    int    `json:"review_status" validate:"" verify:"false"`
	IssueDesc       string `json:"issue_desc" validate:"" verify:"false"`
	IssueRisk       string `json:"issue_risk" validate:"" verify:"false"`
	IssueSuggestion string `json:"issue_suggestion" validate:"" verify:"false"`
}

type StatReqDto struct {
	easy.RequestDto
	Start string `json:"start" validate:"" verify:"false"`
	End   string `json:"end" validate:"" verify:"false"`
}

type IssueshowReqDto struct {
	easy.RequestDto
	ID int64 `json:"id" validate:"" verify:"false"`
}

type UploadBodyResult struct {
	SQL      string `json:"sql" validate:""`
	Filename string `json:"filename" validate:""`
	FnName   string `json:"fn_name" validate:""`
	Key      string `json:"key" validate:""`
	Type     string `json:"type" validate:""`
	Database string `json:"database" validate:""`
	Instance string `json:"instance" validate:""`
}

type UploadBody struct {
	Repo      string              `json:"repo" validate:""`
	Rd        string              `json:"rd" validate:""`
	Result    []*UploadBodyResult `json:"result" validate:""`
	CommitURL string              `json:"commit_url" validate:""`
}

func (r *UploadReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}
