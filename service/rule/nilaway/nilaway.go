/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: nilaway业务封装
 * 可根据业务自行扩展
 */

// nolint:lll
package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"html/template"
	"regexp"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/rule/nilaway"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity/remote"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化即可
type NilawayService struct {
	easy.Service

	// 初始化Dao的位置，切记是指针类型，不需要初始化
	ScanBatchDao *dao.ScanBatchesDao

	LintIssueDao *dao.LintIssuesDao
}

// LintIssueView - 用于模板渲染的结构体
type LintIssueView struct {
	*entity.LintIssues
	CodeLink string `json:"code_link"` // 代码链接
}

// 初始化 必须传入上下文
func NewNilawayService(ctx *easy.Context) *NilawayService {
	service := &NilawayService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewNilawayService 后都会走的逻辑
 */
func (t *NilawayService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * listByRepo - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *NilawayService) ListByRepo(req *dto.ListByRepoReqDto) (*dto.ListByRepoResDto, error) {
	res := &dto.ListByRepoResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	slice, err := t.LintIssueDao.ListNotFixedByRepo(req.RepoName)
	if err != nil {
		t.SLog.Warning("get lint issue error").Set("repo", req.RepoName).SetErr(err).Print()
		return nil, err
	}

	res.RepoName = req.RepoName
	res.Issues = make([]*dto.ListByRepoResDtoIssues, 0)
	for _, item := range slice {
		issue := &dto.ListByRepoResDtoIssues{}

		issue.Username = item.Username
		issue.Fingerprint = item.Fingerprint
		issue.CodeContext = item.CodeContext
		issue.Col = int(item.Col)
		issue.Line = int(item.Line)
		issue.Ignored = item.Ignored == 1
		issue.FirstSeenAt = item.FirstSeenAt
		issue.LastSeenAt = item.LastSeenAt
		issue.Message = item.Message
		issue.RelFilePath = item.RelFilePath
		issue.Status = item.Status

		res.Issues = append(res.Issues, issue)
	}

	return res, nil
}

/*
 * ignoreBatch - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *NilawayService) IgnoreBatch(req *dto.IgnoreBatchReqDto) (*dto.IgnoreBatchResDto, error) {
	res := &dto.IgnoreBatchResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	r, err := t.ScanBatchDao.GetByBatchUID(req.BatchUID)
	if err != nil {
		t.SLog.Warning("get scan batch error").Set("batch uid", req.BatchUID).SetErr(err).Print()
		return nil, err
	}

	r.Ignored = 1
	_, err = t.ScanBatchDao.Update(r)
	if err != nil {
		t.SLog.Warning("update scan batch error").Set("batch uid", req.BatchUID).SetErr(err).Print()
		return nil, err
	}

	fingerPrints := make([]string, 0)
	if err := json.Unmarshal([]byte(r.Note), &fingerPrints); err != nil {
		t.SLog.Warning("unmarshal note error").Set("batch uid", req.BatchUID).SetErr(err).Print()
		return nil, err
	}

	batchRes, err := t.ScanBatchDao.GetByBatchUID(req.BatchUID)
	if err != nil {
		t.SLog.Warning("get scan batch error").Set("batch uid", req.BatchUID).SetErr(err).Print()
		return nil, err
	}

	var fingerPrintSlice []string
	if err := json.Unmarshal([]byte(batchRes.Note), &fingerPrintSlice); err != nil {
		t.SLog.Warning("unmarshal failed").Set("batch id", req.BatchUID).SetErr(err).Print()
		return nil, err
	}

	lintIssues, err := t.LintIssueDao.GetIssueByFingerprintList(fingerPrintSlice)
	if err != nil {
		t.SLog.Warning("get lint issue error").Set("batch uid", req.BatchUID).SetErr(err).Print()
		return nil, err
	}

	for _, issue := range lintIssues {
		issue.Ignored = 1
		_, err = t.LintIssueDao.Update(issue)
		if err != nil {
			t.SLog.Warning("update lint issue error").Set("batch uid", req.BatchUID).SetErr(err).Print()
			return nil, err
		}
	}

	return res, nil
}

/*
 * ignoreOne - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *NilawayService) IgnoreOne(req *dto.IgnoreOneReqDto) (*dto.IgnoreOneResDto, error) {
	res := &dto.IgnoreOneResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	rowsAffected, err := t.LintIssueDao.SetIgnored(req.RepoName, req.Fingerprint)
	if err != nil {
		t.SLog.Warning("set ignored error").Set("repo", req.RepoName).Set("fingerprint", req.Fingerprint).
			Set("rows affected", rowsAffected).SetErr(err).Print()
		return nil, err
	}

	return res, nil
}

/*
 * saveList - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *NilawayService) SaveList(req *dto.SaveListReqDto) (*dto.SaveListResDto, error) {
	res := &dto.SaveListResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	existLintIssues := t.getExistLintRepo(req.BodyDto.RepoName)
	newIssues := make(map[string]struct{})

	addList := make([]*entity.LintIssues, 0)
	updateList := make([]*entity.LintIssues, 0)

	fingerPrints := make([]string, 0)

	for _, issue := range req.BodyDto.Issues {
		newIssues[issue.Fingerprint] = struct{}{}

		fingerPrints = append(fingerPrints, issue.Fingerprint)
		lintIssue, ok := existLintIssues[issue.Fingerprint]
		if ok {
			lintIssue.LastSeenAt = req.BodyDto.ScanTime
			lintIssue.LastBatchUID = req.BodyDto.BatchUID
			lintIssue.Message = issue.Message
			lintIssue.CodeContext = issue.CodeContext
			lintIssue.Col = int32(issue.Col)
			lintIssue.Line = int32(issue.Line)
			lintIssue.RelFilePath = issue.RelFilePath

			updateList = append(updateList, lintIssue)
		} else {
			addList = append(addList, &entity.LintIssues{
				RelFilePath:  issue.RelFilePath,
				Fingerprint:  issue.Fingerprint,
				Message:      issue.Message,
				Line:         int32(issue.Line),
				Col:          int32(issue.Col),
				RepoName:     req.BodyDto.RepoName,
				CodeContext:  issue.CodeContext,
				Status:       "open",
				Username:     issue.Username,
				CreatedAt:    req.BodyDto.ScanTime,
				FirstSeenAt:  req.BodyDto.ScanTime,
				LastSeenAt:   req.BodyDto.ScanTime,
				LastBatchUID: req.BodyDto.BatchUID,
				CommitURL:    req.BodyDto.CommitURL,
				Rd:           req.BodyDto.CommitUser,
				Ignored:      0,
			})
		}
	}

	if len(addList) != 0 {
		if err := t.LintIssueDao.AddList(addList); err != nil {
			t.SLog.Warning("add lint issues error").SetErr(err).Print()
			return nil, err
		}
	}

	// 把其他不在扫描出来的结果里面，但在表历史记录里面的条目
	// 手动设置为 fixed
	for _, issue := range existLintIssues {
		_, ok := newIssues[issue.Fingerprint]
		if !ok {
			issue.Status = "fixed"
			updateList = append(updateList, issue)
		}
	}

	for _, info := range updateList {
		rowsAffected, err := t.LintIssueDao.Update(info)
		if err != nil {
			t.SLog.Warning("update lint issues error").Set("repo", info.Username).Set("rel_path", info.RelFilePath).
				Set("fingerprint", info.Fingerprint).Set("rowsAffected", rowsAffected).SetErr(err).Print()
			return nil, err
		}
	}

	// 批次表也需要加一下
	fingerPrintByte, err := json.Marshal(fingerPrints)
	if err != nil {
		t.SLog.Warning("marshal error").Set("fingerprints", string(fingerPrintByte)).SetErr(err).Print()
		return nil, err
	}

	if _, err := t.ScanBatchDao.Add(&entity.ScanBatches{
		RepoName: req.BodyDto.RepoName,
		BatchUID: req.BodyDto.BatchUID,
		ScanTime: req.BodyDto.ScanTime,
		Note:     string(fingerPrintByte),
	}); err != nil {
		t.SLog.Warning("add scan batch error").Set("repo", req.BodyDto.RepoName).Set("batch_uid", req.BodyDto.BatchUID).
			Set("scan_time", req.BodyDto.ScanTime).SetErr(err).Print()
		return nil, err
	}

	// 只有新增的情况下才需要CardSend

	if len(addList) == 0 {
		t.SLog.Info("no new issues, no need to send card").Set("repo", req.BodyDto.RepoName).Set("commit_url", req.BodyDto.CommitURL).Set("rd", req.BodyDto.CommitUser).Print()
		return res, nil
	}

	title := fmt.Sprintf("NilAway代码规则扫描-%s", req.BodyDto.RepoName)
	// 后续可新增发给提交的 rd

	receiverList := make([]string, len(conf.Cardsend.NilawayReceiver))
	copy(receiverList, conf.Cardsend.NilawayReceiver)

	if conf.Cardsend.SendUserEnable {
		receiverList = append(receiverList, req.BodyDto.CommitUser)
	}

	for _, userID := range receiverList {
		url := fmt.Sprintf("http://%s:%d/rule/nilaway?method=show&commit_url=%s&send_user=%s", conf.Cardsend.SelfHost, conf.Cardsend.SelfPort, req.BodyDto.CommitURL, userID)

		body := &remote.CardSendPost{
			UserData: &remote.CardSendPostUserData{
				Title:       title,
				ServiceName: req.BodyDto.RepoName,
				Desc:        title,
				Subtitle:    title,
				URL: &remote.CardSendPostUserDataURL{
					URL: &remote.CardSendPostUserDataURLURL{
						Type: "newUrl",
						Contents: &remote.CardSendPostUserDataURLURLContents{
							Title: "点击前往",

							Mobile: &remote.CardSendPostUserDataURLURLContentsMobile{
								OpenType: "app_container",
								URL:      url,
							},
							Pc: &remote.CardSendPostUserDataURLURLContentsPc{
								OpenType: "normal",
								URL:      url,
							},
						},
					},
				},
			},
			ReceiverID:   userID,
			TemplateID:   "b84802d37a624255a044267fba988fd7",
			ReceiverType: "user",
			UserMsg:      false,
			AppName:      "easy_helper",
		}
		header := &remote.CardSendHeader{
			ContentType: "application/json",
		}

		cardSendClient := remote.NewCardSendClient(t.Context)
		if err := cardSendClient.Do(body, header); err != nil {
			t.SLog.Warning("nilaway send card error").Set("repo", req.BodyDto.RepoName).Set("url", url).Print()
			return nil, err
		}
		t.SLog.Info("nilaway send card success").Set("repo", req.BodyDto.RepoName).Set("url", url).Print()
	}

	return res, nil
}

func (t *NilawayService) getExistLintRepo(repo string) map[string]*entity.LintIssues {
	existListIssues, err := t.LintIssueDao.ListByRepo(repo)
	if err != nil {
		t.SLog.Warning("list exist lint issues error").Set("repo", repo).SetErr(err).Print()
		return make(map[string]*entity.LintIssues)
	}

	res := make(map[string]*entity.LintIssues)
	for _, issue := range existListIssues {
		res[issue.Fingerprint] = issue
	}
	return res

}

/*
 * show - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *NilawayService) Show(req *dto.ShowReqDto) ([]byte, error) {
	list, err := t.LintIssueDao.ListIssueByCommitURL(req.CommitURL)
	if err != nil {
		t.SLog.Warning("list issue by commit url error").Set("commit_url", req.CommitURL).SetErr(err).Print()
		return nil, err
	}
	if req.SendUser == "" {
		req.SendUser = "default_user"
	}
	return t.showHTMLByLintIssue(req.CommitURL, req.SendUser, list)
}

func (t *NilawayService) showHTMLByLintIssue(commitURL string, sendUser string, list []*entity.LintIssues) ([]byte, error) {
	// 定义HTML模板
	htmlTemplate := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nilaway 问题检查报告 - {{.CommitURL}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            word-break: break-all;
        }
        .commit-info {
            background-color: #3498db;
            color: white;
            padding: 15px 20px;
            border-bottom: 1px solid #2980b9;
        }
        .commit-info a {
            color: white;
            text-decoration: none;
            font-weight: bold;
        }
        .commit-info a:hover {
            text-decoration: underline;
        }
        .fix-guide {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px 20px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }
        .fix-guide a {
            color: #2e7d32;
            text-decoration: none;
            font-weight: 600;
        }
        .fix-guide a:hover {
            text-decoration: underline;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background-color: #ecf0f1;
            border-bottom: 1px solid #ddd;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .issue-item {
            margin: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .issue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e74c3c;
        }
        .issue-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        .issue-time {
            font-size: 12px;
            color: #7f8c8d;
        }
        .issue-content {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .issue-info {
            background-color: white;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #ddd;
        }
        .issue-info h4 {
            margin: 0 0 10px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .field-row {
            margin: 8px 0;
            font-size: 13px;
        }
        .field-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 80px;
        }
        .field-value {
            color: #333;
            word-break: break-all;
        }
        .code-context-wrapper {
            margin-top: 5px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }
        .code-context-header {
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-bottom: 1px solid #e9ecef;
            font-size: 13px;
            font-weight: 500;
            color: #495057;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .view-full-code {
            color: #007bff;
            text-decoration: none;
            font-size: 12px;
            font-weight: normal;
        }
        .view-full-code:hover {
            text-decoration: underline;
        }
        .code-context {
            background-color: #ffffff;
            padding: 12px;
            margin: 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
            overflow-x: auto;
            color: #333;
            max-height: 200px;
            overflow-y: auto;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }
        .empty-state h3 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .process-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        .process-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .process-info {
            color: #6c757d;
            font-size: 11px;
            margin-bottom: 10px;
            font-style: italic;
        }
        .process-status {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status-unchecked {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-is-issue {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-not-issue {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .process-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        .btn-no-fix {
            background-color: #28a745;
            color: white;
        }
        .btn-no-fix:hover {
            background-color: #218838;
        }
        .btn-false-positive {
            background-color: #007bff;
            color: white;
        }
        .btn-false-positive:hover {
            background-color: #0056b3;
        }
        .btn-known-pending {
            background-color: #dc3545;
            color: white;
        }
        .btn-known-pending:hover {
            background-color: #c82333;
        }
        .btn-known-fixed {
            background-color: #17a2b8;
            color: white;
        }
        .btn-known-fixed:hover {
            background-color: #138496;
        }
        .btn-reset {
            background-color: #6c757d;
            color: white;
        }
        .btn-is-issue {
            background-color: #dc3545;
            color: white;
        }
        .btn-is-issue:hover {
            background-color: #c82333;
        }
        .btn-not-issue {
            background-color: #28a745;
            color: white;
        }
        .btn-not-issue:hover {
            background-color: #218838;
        }
        .code-link {
            color: #007bff;
            text-decoration: none;
        }
        .code-link:hover {
            text-decoration: underline;
        }
        .check-question {
            background-color: #e7f3ff;
            border: 1px solid #b3d7ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            font-weight: bold;
            color: #004085;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .fix-doc-link {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
            font-weight: normal;
        }
        .fix-doc-link:hover {
            text-decoration: underline;
        }
        .fix-doc-section {
            margin-top: 15px;
            padding: 10px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
        }
        .fix-doc-notice {
            color: #856404;
            font-size: 14px;
            font-weight: 500;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Nilaway 问题检查报告</h1>
            <p>提交: {{.CommitURL}}</p>
        </div>
        
        <div class="commit-info">
            <div>🔗 <a href="{{.CommitURL}}" target="_blank">查看提交详情</a></div>
        </div>
        
        {{if .FirstIssue}}
        <div style="background-color: #f8f9fa; padding: 10px 20px; border-bottom: 1px solid #dee2e6; font-size: 14px; color: #6c757d;">
            📁 仓库: {{.FirstIssue.RepoName}} | 👤 RD: {{.FirstIssue.Username}} | 🔍 检查人: {{.SendUser}} | 🕒 生成时间: {{.GeneratedAt}}
        </div>
        {{end}}
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">{{.TotalIssues}}</div>
                <div class="stat-label">总问题数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{.UncheckedCount}}</div>
                <div class="stat-label">未检查</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{.IsIssueCount}}</div>
                <div class="stat-label">是问题</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{.NotIssueCount}}</div>
                <div class="stat-label">不是问题</div>
            </div>
        </div>
        
        <div class="fix-guide">
            📖 修复指南: <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/8MCddZCpuH/k73vwkNTP1/g3ORMkgaAMClV1" target="_blank">Nilaway 问题修复文档</a>
        </div>

        {{if .Issues}}
            {{range .Issues}}
            <div class="issue-item" data-id="{{.ID}}">
                <div class="issue-header">
                    <div class="issue-title">
                        <a href="{{.CodeLink}}" target="_blank" class="code-link">{{.RelFilePath}}:{{.Line}}:{{.Col}}</a>
                    </div>
                    <div class="issue-time">{{.FirstSeenAt}}</div>
                </div>
                
                <div class="issue-content">
                    <div class="issue-info">
                        <h4>问题详情</h4>
                        <div class="field-row">
                            <span class="field-label">文件:</span>
                            <span class="field-value">{{.RelFilePath}}</span>
                        </div>
                        <div class="field-row">
                            <span class="field-label">位置:</span>
                            <span class="field-value">第{{.Line}}行 第{{.Col}}列</span>
                        </div>
                        <div class="field-row">
                            <span class="field-label">用户:</span>
                            <span class="field-value">{{.Username}}</span>
                        </div>
                        <div class="field-row">
                            <span class="field-label">仓库:</span>
                            <span class="field-value">{{.RepoName}}</span>
                        </div>
                        <div class="field-row">
                            <span class="field-label">问题:</span>
                            <div class="field-value problem-message">{{.Message | cleanANSI}}</div>
                        </div>
                        {{if .CodeContext}}
                        <div class="field-row">
                            <span class="field-label">代码:</span>
                            <div class="code-context-wrapper">
                                <div class="code-context-header">
                                    📄 代码上下文 
                                    <a href="{{.CodeLink}}" target="_blank" class="view-full-code">(点击查看完整代码)</a>
                                </div>
                                <pre class="code-context">{{.CodeContext}}</pre>
                            </div>
                        </div>
                        {{end}}
                    </div>
                </div>
                
                <div class="process-panel">
                    <div class="process-header">
                        <span>📝 检查状态:</span>
                        {{if eq .ProcessStatus 0}}
                        <span class="process-status status-unchecked" id="status-{{.ID}}">未检查</span>
                        {{else if eq .ProcessStatus 1}}
                        <span class="process-status status-is-issue" id="status-{{.ID}}">🔴 是问题</span>
                        {{else if eq .ProcessStatus 2}}
                        <span class="process-status status-not-issue" id="status-{{.ID}}">✅ 不是问题</span>
                        {{end}}
                    </div>
                    {{if .ProcessTime}}
                    <div class="process-info">
                        <small>👤 {{.ProcessUser}} 于 {{.ProcessTime}} 检查</small>
                    </div>
                    {{end}}
                    <div class="process-buttons">
                        <button class="btn btn-is-issue" onclick="processItem({{.ID}}, 1, '')">🔴 是问题</button>
                        <button class="btn btn-not-issue" onclick="processItem({{.ID}}, 2, '')">✅ 不是问题</button>
                    </div>
                    <div class="fix-doc-section" id="fix-doc-{{.ID}}" style="{{if eq .ProcessStatus 1}}display: block;{{else}}display: none;{{end}}">
                        <div class="fix-doc-notice">
                            📖 该问题需要修复, 基于Comate自动修复的方法，请参考文档: 
                            <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/8MCddZCpuH/k73vwkNTP1/g3ORMkgaAMClV1" target="_blank" class="fix-doc-link">Nilaway 问题修复指南</a>
                        </div>
                    </div>
                </div>
            </div>
            {{end}}
        {{else}}
            <div class="empty-state">
                <h3>暂无问题记录</h3>
                <p>该提交没有Nilaway问题记录</p>
            </div>
        {{end}}
    </div>
    
    <script>
        // 全局变量
        const PROCESS_USER = '{{.SendUser}}';
        const API_BASE = '{{.APIBase}}';
        
        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function(e) {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
        });
        
        // 更新状态显示
        function updateStatus(issueId, status) {
            const statusElement = document.getElementById('status-' + issueId);
            const statusClasses = ['status-unchecked', 'status-is-issue', 'status-not-issue'];
            const statusTexts = ['未检查', '🔴 是问题', '✅ 不是问题'];
            
            // 清除所有状态类
            statusClasses.forEach(cls => statusElement.classList.remove(cls));
            
            // 添加新状态类和文本
            statusElement.classList.add(statusClasses[status]);
            statusElement.textContent = statusTexts[status];
            
            // 控制修复文档链接的显示
            const fixDocSection = document.getElementById('fix-doc-' + issueId);
            if (status === 1) {
                // 是问题 - 显示修复文档链接
                fixDocSection.style.display = 'block';
                showFixGuide();
            } else {
                // 不是问题或未检查 - 隐藏修复文档链接
                fixDocSection.style.display = 'none';
            }
        }
        
        // 显示修复指南
        function showFixGuide() {
            const existingGuide = document.querySelector('.fix-guide');
            if (!existingGuide) {
                const guideDiv = document.createElement('div');
                guideDiv.className = 'fix-guide';
                guideDiv.innerHTML = '📖 修复指南: <a href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/8MCddZCpuH/k73vwkNTP1/g3ORMkgaAMClV1" target="_blank">Nilaway 问题修复文档</a>';
                
                const statsDiv = document.querySelector('.stats');
                statsDiv.after(guideDiv);
            }
        }
        
        // 显示加载状态
        function setLoading(issueId, loading) {
            const issueItem = document.querySelector('[data-id="' + issueId + '"]');
            if (loading) {
                issueItem.classList.add('loading');
            } else {
                issueItem.classList.remove('loading');
            }
        }
        
        // 单个处理操作
        function processItem(issueId, processStatus, processReason) {
			if (processStatus === 1 && !processReason) {
				showFixGuide();
			}
            // if (!confirm('确认要执行此操作吗？')) {
            //     return;
            // }
            
            setLoading(issueId, true);
            
            const url = API_BASE + '?method=process&process_user=' + encodeURIComponent(PROCESS_USER) + 
                       '&issue_id=' + issueId + '&process_status=' + processStatus + 
                       '&process_reason=' + encodeURIComponent(processReason);
            
            fetch(url, {
                method: 'GET',
                // headers: {
                //     'Content-Type': 'application/json'
                // }
            })
            .then(response => {
                if (response.status === 200) {
                    updateStatus(issueId, processStatus);
                    showMessage('操作成功', 'success');
                } else {
                    throw new Error('请求失败，状态码: ' + response.status);
                }
            })
            .catch(error => {
                console.error('Process error:', error);
                showMessage('操作失败: ' + error.message, 'error');
            })
            .finally(() => {
                setLoading(issueId, false);
            });
        }
        

        
        // 显示消息提示
        function showMessage(message, type) {
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-toast message-' + type;
            messageDiv.textContent = message;
            
            // 添加样式
            const style = document.createElement('style');
            if (!document.querySelector('#message-toast-style')) {
                style.id = 'message-toast-style';
                style.textContent = 
                    '.message-toast { position: fixed; top: 20px; right: 20px; padding: 12px 20px; border-radius: 4px; color: white; font-weight: bold; z-index: 9999; animation: slideIn 0.3s ease-out; }' +
                    '.message-success { background-color: #28a745; }' +
                    '.message-error { background-color: #dc3545; }' +
                    '.message-warning { background-color: #ffc107; color: #212529; }' +
                    '@keyframes slideIn { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }'
                document.head.appendChild(style);
            }
            
            // 添加到页面
            document.body.appendChild(messageDiv);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }
    </script>
</body>
</html>`
	// 创建清理ANSI转义字符的函数
	cleanANSI := func(s string) string {
		// 定义ANSI转义序列的正则表达式
		ansiRegex := regexp.MustCompile(`\x1b\[[0-9;]*m`)
		// 清理ANSI转义字符
		cleaned := ansiRegex.ReplaceAllString(s, "")
		// 将换行符转换为HTML的<br>标签
		cleaned = strings.ReplaceAll(cleaned, "\n", "\n\n")
		return cleaned
	}

	// 创建模板函数映射
	funcMap := template.FuncMap{
		"cleanANSI": cleanANSI,
	}

	// 准备模板数据
	var uncheckedCount, isIssueCount, notIssueCount int

	// 转换为模板渲染结构体并添加代码链接
	issueViews := make([]*LintIssueView, 0, len(list))
	for _, issue := range list {
		issueView := &LintIssueView{
			LintIssues: issue,
			CodeLink:   t.getCommitLink(issue),
		}
		issueViews = append(issueViews, issueView)

		// 统计处理状态
		switch issue.ProcessStatus {
		case 0: // 未检查
			uncheckedCount++
		case 1: // 是问题
			isIssueCount++
		case 2: // 不是问题
			notIssueCount++
		}
	}

	var firstIssue *LintIssueView
	if len(issueViews) > 0 {
		firstIssue = issueViews[0]
	}

	apiBase := fmt.Sprintf("http://%s:%d/rule/nilaway", conf.Cardsend.SelfHost, conf.Cardsend.SelfPort)
	data := map[string]any{
		"CommitURL":      commitURL,
		"SendUser":       sendUser,
		"APIBase":        apiBase,
		"GeneratedAt":    time.Now().Format("2006-01-02 15:04:05"),
		"Issues":         issueViews,
		"FirstIssue":     firstIssue,
		"TotalIssues":    len(issueViews),
		"UncheckedCount": uncheckedCount,
		"IsIssueCount":   isIssueCount,
		"NotIssueCount":  notIssueCount,
	}

	// 解析并执行模板
	tmpl, err := template.New("lint_issues_view").Funcs(funcMap).Parse(htmlTemplate)
	if err != nil {
		t.Context.SLog.Error("parse template error").SetErr(err).Print()
		return nil, err
	}

	var result bytes.Buffer
	err = tmpl.Execute(&result, data)
	if err != nil {
		t.Context.SLog.Error("execute template error").SetErr(err).Print()
		return nil, err
	}

	return result.Bytes(), nil
}

func (t *NilawayService) getCommitLink(issue *entity.LintIssues) string {
	// 默认链接格式
	// https://console.cloud.baidu-int.com/devops/icode/repos/baidu/netdisk/netdisk-star/blob/https://console.cloud.baidu-int.com/devops/icode/repos/baidu/netdisk/netdisk-star/commits/cb0b7ab42397378b896202bfa5df8921b357f306/service/star/dynamic/dynamic.go#L1823

	commitID := getCommitID(issue.CommitURL)
	return fmt.Sprintf("https://console.cloud.baidu-int.com/devops/icode/repos/%s/blob/%s/%s#L%d", issue.RepoName, commitID, issue.RelFilePath, issue.Line)
}

func getCommitID(commitLink string) string {
	// 把链接中的 commitsID 提取出来
	//	https://console.cloud.baidu-int.com/devops/icode/repos/baidu/netdisk/netdisk-star/commits/cb0b7ab42397378b896202bfa5df8921b357f306/service/star/dynamic/dynamic.go

	// 分割字符串获取 commits/ 后面的部分
	parts := strings.Split(commitLink, "commits/")
	if len(parts) < 2 {
		return ""
	}

	// 获取 commit ID 部分
	commitPart := parts[1]

	// 分割第一个 / 前面的部分
	commitID := strings.Split(commitPart, "/")[0]

	return commitID

}

/*
 * ProcessExec - 处理 lint issue 的处理请求
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *NilawayService) ProcessExec(req *dto.ProcessReqDto) (*dto.ProcessResDto, error) {
	resDto := &dto.ProcessResDto{}

	// 解析参数
	issueID := req.IssueID
	processUser := req.ProcessUser
	processStatus := req.ProcessStatus
	processReason := req.ProcessReason

	t.Context.SLog.Info("process request").
		Set("issue_id", issueID).
		Set("process_user", processUser).
		Set("process_status", processStatus).Print()

	// 获取现有记录
	issueIDInt, err := strconv.Atoi(issueID)
	if err != nil {
		t.Context.SLog.Error("invalid issue_id").Set("issue_id", issueID).SetErr(err).Print()
		return nil, fmt.Errorf("无效的issue_id: %s", issueID)
	}

	record, err := t.LintIssueDao.Get(int32(issueIDInt))
	if err != nil {
		t.Context.SLog.Error("get lint issue error").Set("issue_id", issueID).SetErr(err).Print()
		return nil, fmt.Errorf("获取记录失败: %v", err)
	}

	if record == nil {
		t.Context.SLog.Error("lint issue not found").Set("issue_id", issueID).Print()
		return nil, fmt.Errorf("记录不存在: %s", issueID)
	}

	// 解析处理状态
	processStatusInt, err := strconv.Atoi(processStatus)
	if err != nil {
		t.Context.SLog.Error("invalid process_status").Set("process_status", processStatus).SetErr(err).Print()
		return nil, fmt.Errorf("无效的process_status: %s", processStatus)
	}

	// 验证参数：检查状态值是否合法
	if processStatusInt < 0 || processStatusInt > 2 {
		return nil, fmt.Errorf("无效的处理状态, 必须是0(未检查)、1(是问题)或2(不是问题)")
	}

	// 更新处理状态
	record.ProcessStatus = int32(processStatusInt)
	record.ProcessUser = processUser
	record.ProcessReason = processReason
	record.ProcessTime = time.Now().Format("2006-01-02 15:04:05")

	// 保存到数据库
	_, err = t.LintIssueDao.Update(record, "process_status", "process_user", "process_reason", "process_time")
	if err != nil {
		t.Context.SLog.Error("update lint issue error").Set("issue_id", issueID).SetErr(err).Print()
		return nil, fmt.Errorf("更新记录失败: %v", err)
	}

	t.Context.SLog.Info("process success").
		Set("issue_id", issueID).
		Set("process_user", processUser).
		Set("process_status", processStatus).Print()

	return resDto, nil
}

/*
 * process - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *NilawayService) Process(req *dto.ProcessReqDto) (*dto.ProcessResDto, error) {
	res := &dto.ProcessResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response
	return res, nil
}
