/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: sa业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"encoding/json"
	"os"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/rule/sa"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type SaService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
	SaRuleInfoDao *dao.SaRuleInfoDao
}

// 初始化 必须传入上下文
func NewSaService(ctx *easy.Context) *SaService {
	service := &SaService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewSaService 后都会走的逻辑
 */
func (t *SaService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * query - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *SaService) Query(req *dto.QueryReqDto) (*dto.QueryResDto, error) {
	res := &dto.QueryResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response
	info, err := t.SaRuleInfoDao.GetByInfo(req.Icode, req.RuleName)
	if err != nil {
		t.SLog.Warning("get info err").SetErr(err).Print()
		return nil, err
	}
	res.Icode = info.Repo
	res.App = info.App
	res.Data = info.Data
	res.Cnt = info.Cnt
	return res, nil
}

/*
 * listIcode - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *SaService) ListIcode(req *dto.ListIcodeReqDto) (*dto.ListIcodeResDto, error) {
	res := &dto.ListIcodeResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response
	list, err := t.SaRuleInfoDao.ListApp()
	if err != nil {
		t.SLog.Warning("list app err").SetErr(err).Print()
		return nil, err
	}
	for _, item := range list {
		res.List = append(res.List, &dto.ListIcodeResDtoList{
			Icode: item.Repo,
			App:   item.App,
		})
	}
	return res, nil
}

func (t *SaService) InsertData() error {
	resultByte, err := os.ReadFile("result.json")
	if err != nil {
		return err
	}
	var result []AppInfo
	if err := json.Unmarshal(resultByte, &result); err != nil {
		return err
	}

	d := dao.NewSaRuleInfoDao(t.Context)

	repoMap := make(map[string]struct{})
	var list []*entity.SaRuleInfo
	for _, item := range result {
		if _, ok := repoMap[item.Icode]; ok {
			continue
		}
		repoMap[item.Icode] = struct{}{}
		list = append(list, &entity.SaRuleInfo{
			Repo: item.Icode,
			App:  item.AppName,
			Cnt:  int32(item.Cnt),
			Data: item.Text,
			Rule: "recover",
		})
	}

	if err := d.AddList(list); err != nil {
		return err
	}
	return nil

}

type AppInfo struct {
	AppName string
	Icode   string
	Cnt     int
	Text    string
	Rule    string
}
