/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: SaService单元测试
 */
package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/rule/sa"
)

// query接口业务测试入口
func TestSaQuery(t *testing.T) {
	// 注册驱动
	SaMockRegistry(t)
	s := NewSaService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.QueryReqDto{}
	// 设置reqDto参数 测试数据
	resDto, errDto := s.Query(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// listIcode接口业务测试入口
func TestSaListIcode(t *testing.T) {
	// 注册驱动
	SaMockRegistry(t)
	s := NewSaService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.ListIcodeReqDto{}
	// 设置reqDto参数 测试数据
	resDto, errDto := s.ListIcode(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// 注册驱动
func SaMockRegistry(t *testing.T) {
	var err error
	// 初始化配置文件,如果在根目录启动则不用设置
	config_mock.SetConfRootPath("../conf")
	err = easy.Init("")
	assert.Nil(t, err)
	// 注册直连sql驱动
	err = mock.MockSqlDirectRegistry("mysql_cloud_tangram_dump",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "tangram_dump"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_zhongzhenyan",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "zhongzhenyan"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_fault-panel",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "fault-panel"})
	assert.Nil(t, err)
	// 注册直连redis驱动
	err = mock.MockRedisDirectRegistry("cache-bj", "************:8841")
	assert.Nil(t, err)
	// 注册直连http驱动, 测试的host链接由业务指定
	//示例:err = mock.MockHttpDirectRegistry("confName","http://localhost:8080")
	err = mock.MockHttpDirectRegistry("naclocal", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("nacmaster", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("feStatistics", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("userManager", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("userInfo", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("easycode", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("CardSend", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("tangramConfig", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("AppList", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("ufcBypass", "")
	assert.Nil(t, err)
	if err != nil {
		assert.Nil(t, err)
	}
}
