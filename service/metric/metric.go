/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: metric业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"errors"
	"strconv"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/codemetric"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/metric"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/metric"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity/remote"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/easyutils"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/errs"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/codemetric"
)

const LimitValue = 1000

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type MetricService struct {
	easy.Service

	// 初始化Dao的位置，切记是指针类型，不需要初始化
	EeIncreDao	*dao.EeIncreLineDao

	EeTotalDao	*dao.EeTotalLineDao
}

// 初始化 必须传入上下文
func NewMetricService(ctx *easy.Context) *MetricService {
	service := &MetricService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * add - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *MetricService) Add(req *dto.AddReqDto) (*dto.AddResDto, error) {
	// 编写业务代码组装Response
	newCtx, err := easy.WithOldContext(t.Context)
	if err != nil {
		t.SLog.Error("with old context").SetErr(err).Print()
		return nil, err
	}

	svc := NewMetricService(newCtx)

	go func(svc *MetricService, req *dto.AddReqDto) {
		_, err := svc.addTask(req)
		if err != nil {
			svc.SLog.Error("add task error").SetErr(err).Print()
			//	return
		}
	}(svc, req)

	return nil, nil
}

func (t *MetricService) addTask(req *dto.AddReqDto) (*dto.AddResDto, error) {
	res := &dto.AddResDto{}
	repo := req.BodyDto.Repo
	userName := req.BodyDto.User

	easyCodeClient := remote.NewEasycodeClient(t.Context)
	query := &remote.EasycodeGet{
		Icode: repo,
	}
	result, err := easyCodeClient.Do(query)
	if err != nil {
		t.SLog.Warning("get repo code failed").Set("repo", repo).SetErr(err).Print()
		return nil, err
	}

	newTotalLine := easyutils.GetTotalLine(result)
	if newTotalLine == 0 {
		t.SLog.Warning("repo line is empty").Set("repo", repo).Print()
		return nil, errors.New("newTotalLine is empty")
	}
	cnt, err := t.EeTotalDao.Count(repo)
	if err != nil {
		t.SLog.Error("count err").Set("repo", repo).SetErr(err).Print()
		return nil, err
	}
	if cnt > 0 {
		// 非首次把数据落到数据库
		totalLine, err := t.EeTotalDao.Get(repo)
		if err != nil {
			t.SLog.Warning("repo line get error").SetErr(err).Print()
			return nil, err
		}

		oldTotalLine := totalLine.TotalLine

		mtime := easyutils.UnixTimeStampToString(totalLine.Mtime)
		t.SLog.Info("repo line info").Set("repo", repo).Set("last line", oldTotalLine).Set("now line", newTotalLine).Set("last mtime", mtime).Print()
		increCnt := newTotalLine - int(totalLine.TotalLine)
		totalLine.TotalLine = int32(newTotalLine)
		totalLine.Mtime = time.Now().Unix()

		if increCnt > 0 {
			// 原子更新两个表的数据
			increLine := &entity.EeIncreLine{}
			increLine.Repo = repo
			increLine.IncreLine = int32(increCnt)
			increLine.Username = userName
			increLine.CreateAt = time.Now().Unix()

			increLineCnts := easyutils.SplitNum(increCnt)

			dbName := "mysql_cloud_netdisk_easy_coderule"
			err := t.Transaction.ExecWithTx(dbName, nil, func(tx *easy.Tx) error {
				if err := t.updateTotalLineWithTx(tx, totalLine); err != nil {
					t.SLog.Error("update repo line failed").Set("repo", repo).SetErr(err).Print()
					return err
				}

				if err := t.addIncreLineWithTx(tx, increLine, increLineCnts); err != nil {
					t.SLog.Warning("add repo line failed").Set("repo", repo).SetErr(err).Print()
					return err
				}
				return nil
			})
			if err != nil {
				t.SLog.Error("update repo line with transaction failed").Set("repo", repo).SetErr(err).Print()
				return nil, err
			}
			return res, nil
		} else if increCnt == 0 {
			// 增量行数为0就不记录了，打日志即可
			t.SLog.Warning("incre line is zero").Set("repo", repo).Set("user", userName).Set("new", newTotalLine).Set("old", oldTotalLine).Print()
			return res, nil
		} else {
			// 更新后的数据小于更新前的表明 rd 有删减，更新总代码行即可
			t.SLog.Warning("new repo line is less than old repo line").Set("repo", repo).Set("new", newTotalLine).Set("old", oldTotalLine).Print()
			rowsEffected, err := t.EeTotalDao.Update(totalLine)
			if err != nil {
				t.SLog.Warning("update repo line failed").Set("total line", totalLine).SetErr(err).Print()
				return nil, err
			}
			if rowsEffected < 0 {
				t.SLog.Warning("update repo line failed").Set("total line", totalLine).SetErr(err).Print()
				return nil, errors.New("update repo line failed")
			}
			return res, nil
		}
	} else {
		dbName := "mysql_cloud_netdisk_easy_coderule"

		totalLine := &entity.EeTotalLine{}
		timeNow := time.Now().Unix()
		totalLine.Ctime = timeNow
		totalLine.Mtime = timeNow
		totalLine.TotalLine = int32(newTotalLine)
		totalLine.Repo = repo

		increLine := &entity.EeIncreLine{}

		increLine.Repo = repo
		increLine.CreateAt = timeNow
		increLine.Username = userName
		increLineCnts := easyutils.SplitNum(newTotalLine)

		err := t.Transaction.ExecWithTx(dbName, nil, func(tx *easy.Tx) error {
			if err := t.addTotalLineWithTx(tx, totalLine); err != nil {
				t.SLog.Error("update repo line failed").Set("repo", repo).SetErr(err).Print()
				return err
			}
			if err := t.addIncreLineWithTx(tx, increLine, increLineCnts); err != nil {
				t.SLog.Warning("add repo line failed").Set("repo", repo).SetErr(err).Print()
				return err
			}
			return nil
		})
		if err != nil {
			t.SLog.Error("add repo line with transaction failed").Set("repo", repo).SetErr(err).Print()
			return nil, err
		}
		return res, nil
	}
}

func (t *MetricService) addIncreLineWithTx(tx *easy.Tx, eeIncreLine *entity.EeIncreLine, increLines []int) error {
	for _, increLine := range increLines {
		eeIncreLine.IncreLine = int32(increLine)
		lastInsertID, err := tx.Model(eeIncreLine).Create()
		if err != nil {
			t.SLog.Warning("insert eeIncreLine failed").Set("incre line", eeIncreLine).SetErr(err).Print()
			return err
		}

		if lastInsertID < 0 {
			t.SLog.Error("insert eeIncreLine failed").Set("lastInsertID", lastInsertID).Set("incre line", eeIncreLine).Print()
			return errs.AppDBInsertFailed
		}
	}
	return nil
}

func (t *MetricService) updateTotalLineWithTx(tx *easy.Tx, eeTotalLine *entity.EeTotalLine) error {
	rowsAffected, err := tx.Model(eeTotalLine).Update()
	if err != nil {
		t.SLog.Error("db insert total record failed").Set("rowsAffected", rowsAffected).SetErr(err).Print()
		return errs.AppDBUpdateFailed
	}

	if rowsAffected < 0 {
		t.SLog.Error("db insert total record failed").Set("rowsAffected", rowsAffected).Print()
		return errs.AppDBUpdateFailed
	}
	return nil
}

func (t *MetricService) addTotalLineWithTx(tx *easy.Tx, eeTotalLine *entity.EeTotalLine) error {
	lastInserID, err := tx.Model(eeTotalLine).Create()
	if err != nil {
		t.SLog.Error("db insert total record failed").Set("lastInserID", lastInserID).SetErr(err).Print()
		return errs.AppDBInsertFailed
	}

	if lastInserID < 0 {
		t.SLog.Error("db insert total record failed").Set("lastInserID", lastInserID).Print()
		return errs.AppDBInsertFailed
	}
	return nil
}

/*
 * incre - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *MetricService) Incre(req *dto.IncreReqDto) (*dto.IncreResDto, error) {
	res := &dto.IncreResDto{}
	// 编写业务代码组装Response

	// 屏蔽前后端统计数据
	if conf.Control.CloseAllData {
		return res, nil
	}

	timeFormat := req.TimeFormat
	var start int64
	var end int64
	if timeFormat == "1" {
		startTime, err := time.Parse("2006-01-02", req.Start)
		if err != nil {
			t.SLog.Error("parse start time failed").Set("req", req).SetErr(err).Print()
			return nil, err
		}

		endTime, err := time.Parse("2006-01-02", req.End)
		if err != nil {
			t.SLog.Error("parse start time failed").Set("req", req).SetErr(err).Print()
			return nil, err
		}
		endTime = endTime.Add(24 * time.Hour)

		start = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.Local).Unix()
		end = time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, time.Local).Unix()
	} else if timeFormat == "2" {
		startTime, err := strconv.Atoi(req.Start)
		if err != nil {
			t.SLog.Error("parse start time failed").Set("req", req).SetErr(err).Print()
			return nil, err
		}
		endTime, err := strconv.Atoi(req.End)
		if err != nil {
			t.SLog.Error("parse start time failed").Set("req", req).SetErr(err).Print()
			return nil, err
		}
		start = int64(startTime)
		end = int64(endTime)
	} else {
		return nil, errors.New("time_format is illegal, it should be 1 or 2")
	}

	// 用于判断是否过滤1000行，默认为 false, 过滤掉
	notFilter := req.Notfilter
	var backendIncreTotalCnt int32 = 0
	if !conf.Control.CloseBackendData {
		if conf.Control.IsRepoRecord {
			// 落库数据
			cmSvc := service.NewCodemetricService(t.Context)
			increReq := &codemetric.IncreReqDto{
				StartTime:	start,
				EndTime:	end,
			}

			increRes, err := cmSvc.Incre(increReq)
			if err != nil {
				t.SLog.Error("codemetric incre err").SetErr(err).Print()
				return nil, err
			}
			for _, item := range increRes.Data {
				if !notFilter && ExceedLimit(item.EasyTotalCnt+item.UserTotalCnt) {
					continue
				}

				backendIncreTotalCnt += item.EasyTotalCnt
				outputSlice := ConvertCodeMetricResToMetricDataSlice(item)
				res.Data = append(res.Data, outputSlice...)
			}
		} else {
			// easy 侧生成的数据, 不落代码库的数据
			list, err := t.EeIncreDao.List(start, end)
			if err != nil {
				t.SLog.Error("EeTotalDao List err").SetErr(err).Set("start", start).Set("end", end).Print()
				return nil, err
			}

			for _, item := range list {
				backendIncreTotalCnt += item.IncreLine

				tmpData := &dto.IncreResDtoData{}
				tmpData.Repo = item.Repo
				tmpData.CreateAt = easyutils.UnixTimeStampToString(item.CreateAt)
				tmpData.IncreLine = item.IncreLine
				tmpData.Lang = "go"
				tmpData.User = item.Username
				res.Data = append(res.Data, tmpData)
			}
		}
	}
	var frontEndLineCnt int32 = 0
	// 如果query中有fe字段，则把前端的一块展示出来
	if req.Fe && !conf.Control.CloseFeData {
		client := remote.NewFeStatisticsClient(t.Context)

		query := &remote.FeStatisticsGet{}
		query.St = start
		query.Et = end

		header := &remote.FeStatisticsHeader{}
		header.ContentType = "application/x-www-form-urlencoded"
		result, err := client.Do(query, header)
		if err != nil {
			t.SLog.Error("fe statistics get err").SetErr(err).Print()
			return nil, err
		}

		for _, item := range result.Data {
			frontEndLineCnt += item.IncreLine

			tmpData := &dto.IncreResDtoData{}
			tmpData.Repo = item.Repo
			tmpData.CreateAt = easyutils.UnixTimeStampToString(item.CreateAt)
			tmpData.IncreLine = item.IncreLine
			tmpData.Lang = item.Lang
			tmpData.User = item.User
			res.Data = append(res.Data, tmpData)
		}
		res.FrontIncreTotalCnt = frontEndLineCnt
	}

	res.BackendIncreTotalCnt = backendIncreTotalCnt
	res.IncreTotalCnt = backendIncreTotalCnt + frontEndLineCnt

	// 返回Response
	return res, nil
}

func ConvertCodeMetricResToMetricDataSlice(input *codemetric.IncreResDtoData) []*metric.IncreResDtoData {
	initIncreRes := &metric.IncreResDtoData{}
	initIncreRes.Repo = input.RepoName
	initIncreRes.IncreLine = input.EasyTotalCnt
	initIncreRes.Lang = "go"
	initIncreRes.CreateAt = easyutils.UnixTimeStampToString(input.CreateAt)
	initIncreRes.User = input.CommitUser

	codeCnt := input.EasyTotalCnt
	if codeCnt <= 1000 {
		return []*metric.IncreResDtoData{initIncreRes}
	}

	res := make([]*metric.IncreResDtoData, 0)
	// 超过 1000 行，需要分块
	numSlice := easyutils.SplitNum(int(codeCnt))
	for _, num := range numSlice {
		tmpItem := CopyIncreResDtoData(initIncreRes)
		tmpItem.IncreLine = int32(num)
		res = append(res, tmpItem)
	}
	return res
}

func CopyIncreResDtoData(input *metric.IncreResDtoData) *dto.IncreResDtoData {
	output := &dto.IncreResDtoData{}
	output.Repo = input.Repo
	output.CreateAt = input.CreateAt
	output.IncreLine = input.IncreLine
	output.Lang = input.Lang
	output.User = input.User
	return output
}

func ExceedLimit(cnt int32) bool {
	return cnt > int32(conf.Control.LimitNumber)
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewMetricService 后都会走的逻辑
 */
func (t *MetricService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}
