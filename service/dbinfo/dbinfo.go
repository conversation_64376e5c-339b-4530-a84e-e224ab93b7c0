/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: dbinfo业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"fmt"
	"strings"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/dbinfo"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity/remote"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type DbinfoService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
	TDrdsInstanceDao *dao.TDrdsInstanceDao
}

// 初始化 必须传入上下文
func NewDbinfoService(ctx *easy.Context) *DbinfoService {
	service := &DbinfoService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewDbinfoService 后都会走的逻辑
 */
func (t *DbinfoService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * queryAccess - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */

/*
 * queryConfig - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *DbinfoService) QueryConfig(req *dto.QueryConfigReqDto) (*dto.QueryConfigResDto, error) {
	res := &dto.QueryConfigResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	appListClient := remote.NewAppListClient(t.Context)
	appListClient.SetMethod("GET")
	result, err := appListClient.Do()
	if err != nil {
		t.SLog.Warning("query config error").SetErr(err).Print()
		return nil, err
	}

	var appName string
	for _, item := range result.Data.List {
		if item.Icode == req.Icode {
			appName = item.Name
		}
	}

	if appName == "" {
		t.SLog.Warning("app name is empty").Set("icode", req.Icode).Print()
		return nil, fmt.Errorf("app name is empty, icode is %s", req.Icode)
	}

	tangramConfigClient := remote.NewTangramConfigClient(t.Context)
	query := &remote.TangramConfigGet{
		Method:      "list",
		Type:        "mysql",
		ServiceName: appName,
	}

	header := &remote.TangramConfigHeader{
		AMISUSER: "zhongzhenyan",
	}

	resp, err := tangramConfigClient.Do(query, header)
	if err != nil {
		t.SLog.Warning("query config error").SetErr(err).Print()
		return nil, err
	}

	for _, row := range resp.Data.Rows {
		for _, retry := range row.Retrys {
			if retry.Suffix != "online" {
				continue
			}

			item := &dto.QueryConfigResDtoData{
				GroupName:   retry.GroupName,
				Database:    retry.Item.MysqldialoptDatabase,
				AddressType: retry.AddressType,
				To:          retry.To,
			}

			instanceName, err := t.queryInstance(req.Icode, retry.AddressType, retry.To)
			if err == nil {
				item.Instance = instanceName
			}

			res.Data = append(res.Data, item)
		}
	}

	return res, nil
}

func (t *DbinfoService) queryInstance(icode string, addressType string, to string) (string, error) {
	if addressType == "direct" {
		toSlice := strings.Split(to, ";")
		for _, toItem := range toSlice {
			info, err := t.TDrdsInstanceDao.QueryAllByAccess(toItem)
			if err != nil {
				t.SLog.Warning("query instance error").Set("address type", addressType).Set("to", to).SetErr(err).Print()
				continue
			}

			if info == nil {
				t.SLog.Warning("query instance error").Set("address type", addressType).Set("to", to).Print()
				continue
			}
			return info.Name, nil
		}

		return "", fmt.Errorf("instance not found")

	} else if addressType == "bns" {
		res := t.queryInstanceByBns(to)
		fmt.Println("res: ", res)
		return res, nil
	} else if addressType == "ufc" {
		// 国内还是海外
		isGlobal := strings.Contains(icode, "netdisk-global")

		ufcBypassClient := remote.NewUfcBypassClient(t.Context)
		if isGlobal {
			ufcBypassClient.Proxy = conf.Cardsend.UfcGlobalProxyAddr
			ufcBypassClient.SetToServiceUfcName("127.0.0.1:8240")
		} else {
			t.SLog.Warning("match inner ufc-backup").Print()
			ufcBypassClient.SetToServiceUfcName("************:80")

		}

		query := &remote.UfcBypassGet{
			Cmd:             "get_backend",
			XUfcServiceName: to,
		}

		header := &remote.UfcBypassHeader{
			XUfcSelfServiceName: "easyCodeRuleDataServer-self",
		}

		result, err := ufcBypassClient.Do(query, header)
		if err != nil {
			t.SLog.Warning("query instance error").Set("addresss type", addressType).Set("to", to).Set("result", result).
				Set("query", query).Set("header", header).SetErr(err).Print()
			return "", err
		}
		t.SLog.Warning("result info").Set("result", result).Print()

		if strings.Contains(result.Bns, "fakebns") {
			addr := fmt.Sprintf("%s:%d", result.Host, result.Port)
			info, err := t.TDrdsInstanceDao.QueryAllByAccess(addr)
			if err != nil {
				t.SLog.Warning("query instance error").Set("addresss type", addressType).Set("to", to).SetErr(err).Print()
				return "", err
			}
			return info.Name, nil
		}
		res := t.queryInstanceByBns(result.Bns)
		return res, nil

	}
	t.SLog.Warning("query instance error").Set("addresss type", addressType).Set("to", to).Print()
	return "", fmt.Errorf("address type not supported")

	//return info, nil
}

func (t *DbinfoService) queryInstanceByBns(bns string) string {
	fmt.Println("bns:", bns)
	hasBmi := strings.Contains(bns, "-bmi")
	if !hasBmi {
		return bns
	}

	slice := strings.Split(bns, "-bmi")
	if len(slice) <= 1 {
		return bns
	}

	subSlice := strings.Split(slice[0], ".")
	if len(subSlice) < 1 {
		return bns
	}
	if len(subSlice) == 1 {
		return subSlice[0]
	}

	return subSlice[1]

}
