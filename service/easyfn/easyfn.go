/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: easyfn业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/easyfn"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/easyutils"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type EasyfnService struct {
	easy.Service

	// 初始化Dao的位置，切记是指针类型，不需要初始化
	EasyFnMsgDao	*dao.EasyFnMsgDao
}

// 初始化 必须传入上下文
func NewEasyfnService(ctx *easy.Context) *EasyfnService {
	service := &EasyfnService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewEasyfnService 后都会走的逻辑
 */
func (t *EasyfnService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * add - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *EasyfnService) Add(req *dto.AddReqDto) (*dto.AddResDto, error) {
	res := &dto.AddResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	body := req.BodyDto
	easyFnMsg := &entity.EasyFnMsg{}
	easyFnMsg.Repo = body.Repo
	easyFnMsg.Branch = body.Branch
	easyFnMsg.CommitAt = body.CommitAt
	easyFnMsg.CommitID = body.CommitID
	easyFnMsg.CreateAt = body.CreateAt
	easyFnMsg.Data = body.Data

	if err := t.EasyFnMsgDao.AddOrUpdateFnMsg(easyFnMsg); err != nil {
		t.SLog.Warning("add fn msg failed").SetErr(err).Print()
		return nil, err
	}

	return res, nil
}

/*
 * get - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *EasyfnService) Get(req *dto.GetReqDto) (*dto.GetResDto, error) {
	res := &dto.GetResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response
	fnMsg, err := t.EasyFnMsgDao.Get(req.Repo, req.Branch)
	if err != nil {
		t.SLog.Warning("get fn msg failed").SetErr(err).Print()
		return nil, err
	}

	res.Repo = fnMsg.Repo
	res.Branch = fnMsg.Branch
	res.CommitAt = fnMsg.CommitAt
	res.CommitID = fnMsg.CommitID
	res.CreateAt = easyutils.UnixTimeStampToString(fnMsg.CreateAt)

	var data []any
	if err := json.Unmarshal([]byte(fnMsg.Data), &data); err != nil {
		t.SLog.Warning("unmarshal data failed").SetErr(err).Print()
		return nil, err
	}
	res.Data = data
	// 	res.Data =
	// res.CreateAt = unix.

	return res, nil
}

/*
 * branchquery - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *EasyfnService) Branchquery(req *dto.BranchqueryReqDto) (*dto.BranchqueryResDto, error) {
	res := &dto.BranchqueryResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response
	branchs, err := t.EasyFnMsgDao.GetBranch(req.Repo)
	if err != nil {
		t.SLog.Warning("get fn msg failed").SetErr(err).Print()
		return nil, err
	}
	res.Branchs = branchs

	return res, nil
}
