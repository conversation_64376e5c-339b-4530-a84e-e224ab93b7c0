/*
* 主体代码由Easy生成，**后续update不会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: upload业务封装
* 可根据业务自行扩展
 */
package service

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/sergi/go-diff/diffmatchpatch"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/sqldata"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity/remote"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type UploadService struct {
	easy.Service

	// 初始化Dao的位置，切记是指针类型，不需要初始化
	ReqDto	*dto.UploadReqDto

	SQLResultsDao		*dao.SQLScanResultsDao
	SQLDiffRecordDao	*dao.SQLDiffRecordDao
}

// 初始化 必须传入上下文
func NewUploadService(ctx *easy.Context) *UploadService {
	service := &UploadService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

func (s *UploadService) UploadExec(reqDto *dto.UploadReqDto) (easy.ResDto, error) {
	s.ReqDto = reqDto
	resDto := &dto.UploadResDto{}

	var list []*entity.SQLScanResults

	sqlList, err := s.SQLResultsDao.GetListByRepo(reqDto.BodyDto.Repo)
	if err != nil {
		s.Context.SLog.Error("get sql list error").Set("repo", reqDto.BodyDto.Repo).SetErr(err).Print()
		return nil, err
	}

	if len(sqlList) > 0 {
		s.SLog.Warning("sql list len").Set("len", len(sqlList)).Print()
	}
	m := make(map[string]*entity.SQLScanResults)
	for _, item := range sqlList {
		m[item.RealKey] = item
	}

	var diffList []*entity.SQLDiffInfo

	for _, item := range reqDto.BodyDto.Result {
		realKey := fmt.Sprintf("%s_%s_%s_%s", reqDto.BodyDto.Repo, item.Filename, item.FnName, item.Key)

		// realKeyHash := hex.EncodeToString(sha256.New().Sum([]byte(realKey)))

		hash := sha256.Sum256([]byte(realKey))
		realKeyHash := hex.EncodeToString(hash[:])

		// s.SLog.Warning("result item info").Set("real key", realKey).Set("real key hash", realKeyHash).Set("filename", item.Filename).Set("key", item.Key).Print()

		newEntityResult := &entity.SQLScanResults{
			SQLText:	item.SQL,
			Filename:	item.Filename,
			FnName:		item.FnName,
			KeyName:	item.Key,
			Type:		item.Type,
			CommitURL:	reqDto.BodyDto.CommitURL,
			Repo:		reqDto.BodyDto.Repo,
			Rd:		reqDto.BodyDto.Rd,
			RealKey:	realKeyHash,
			InstanceName:	item.Instance,
			DBName:		item.Database,
		}

		if oldResult, ok := m[realKeyHash]; ok {
			if oldResult.SQLText != newEntityResult.SQLText {
				diffList = append(diffList, &entity.SQLDiffInfo{
					UnicKey:	realKeyHash,
					Old:		m[realKeyHash],
					New:		newEntityResult,
				})
			}
		} else {
			list = append(list, newEntityResult)
		}
	}

	// 处理diff数据
	s.SLog.Warning("diff list len").Set("len", len(diffList)).Print()

	if len(diffList) > 0 {
		if err := s.handleDiffSQL(reqDto.BodyDto.Repo, diffList); err != nil {
			s.Context.SLog.Error("handle diff sql error").Set("repo", reqDto.BodyDto.Repo).SetErr(err).Print()
			return nil, err
		}

		// 更新旧的数据记录为新的数据
		for _, diff := range diffList {
			if diff.Old != nil && diff.New != nil {
				newData := diff.New
				// 更新数据库中的记录
				diff.Old.SQLText = newData.SQLText
				diff.Old.Filename = newData.Filename
				diff.Old.FnName = newData.FnName
				diff.Old.KeyName = newData.KeyName
				diff.Old.Type = newData.Type
				diff.Old.CommitURL = newData.CommitURL
				diff.Old.Rd = newData.Rd

				if _, err := s.SQLResultsDao.Update(diff.Old); err != nil {
					s.Context.SLog.Error("update sql result error").Set("id", diff.Old.ID).SetErr(err).Print()
					// 不返回错误，继续处理其他记录
				} else {
					s.SLog.Warning("update sql result success").Set("id", diff.Old.ID).Print()
				}
			}
		}
	}

	if len(list) == 0 {
		s.Context.SLog.Warning("no new sql result").Set("repo", reqDto.BodyDto.Repo).Set("commit_url", reqDto.BodyDto.CommitURL).Set("rd", reqDto.BodyDto.Rd).Print()
		return resDto, nil
	}

	if err := s.SQLResultsDao.AddList(list); err != nil {
		s.Context.SLog.Error("add sql results error").Set("repo", reqDto.BodyDto.Repo).
			Set("commit_url", reqDto.BodyDto.CommitURL).Set("rd", reqDto.BodyDto.Rd).SetErr(err).Print()
		return nil, err
	}

	return resDto, nil
}

func (s *UploadService) handleDiffSQL(repoName string, diffList []*entity.SQLDiffInfo) (err error) {
	if len(diffList) == 0 {
		return nil
	}

	// 用于决定是 insert 还是 update
	_, err = s.SQLDiffRecordDao.GetListByRepo(repoName)
	if err != nil {
		s.Context.SLog.Error("get sql diff records error").Set("repo", repoName).SetErr(err).Print()
		return err
	}

	// 生成批次ID
	batchID := fmt.Sprintf("batch_%d_%s", time.Now().Unix(), s.ReqDto.BodyDto.Repo)

	var records []*entity.SQLDiffRecord

	dmp := diffmatchpatch.New()
	for _, diff := range diffList {
		record := &entity.SQLDiffRecord{
			Repo:		s.ReqDto.BodyDto.Repo,
			Rd:		s.ReqDto.BodyDto.Rd,
			CommitURL:	s.ReqDto.BodyDto.CommitURL,
			UnicKey:	diff.UnicKey,
			BatchID:	batchID,
		}

		// 填充旧数据信息
		if diff.Old != nil {
			record.OldSQLText = diff.Old.SQLText
			record.OldFilename = diff.Old.Filename
			record.OldFnName = diff.Old.FnName
			record.OldKeyName = diff.Old.KeyName
			record.OldType = diff.Old.Type
			record.OldInstanceName = diff.Old.InstanceName
			record.OldDBName = diff.Old.DBName
		}

		// 填充新数据信息
		if diff.New != nil {
			// 尝试断言为 *UploadBodyResult 类型
			s.SLog.Warning("diff new is match").Print()
			record.NewSQLText = diff.New.SQLText
			record.NewFilename = diff.New.Filename
			record.NewFnName = diff.New.FnName
			record.NewKeyName = diff.New.KeyName
			record.NewType = diff.New.Type
			record.NewInstanceName = diff.New.InstanceName
			record.NewDBName = diff.New.DBName
		}

		diffs := dmp.DiffMain(record.OldSQLText, record.NewSQLText, false)
		record.DiffSQLHTML = dmp.DiffPrettyHtml(diffs)

		records = append(records, record)
	}

	// s.checkRecords(records)
	// 批量插入记录
	if err := s.SQLDiffRecordDao.AddList(records); err != nil {
		s.Context.SLog.Error("add sql diff records error").Set("batch_id", batchID).SetErr(err).Print()
		return err
	}

	s.Context.SLog.Info("successfully saved sql diff records").Set("batch_id", batchID).Set("count", len(records)).Print()

	if err := s.SendCardToAllUser(s.ReqDto.BodyDto.Repo, s.ReqDto.BodyDto.CommitURL); err != nil {
		s.Context.SLog.Error("send card to all user error").Set("repo", s.ReqDto.BodyDto.Repo).Set("commit_url", s.ReqDto.BodyDto.CommitURL).SetErr(err).Print()
	}

	s.Context.SLog.Info("successfully sent card to all user").Set("repo", s.ReqDto.BodyDto.Repo).Set("commit_url", s.ReqDto.BodyDto.CommitURL).Print()

	return nil
}

func (s *UploadService) checkRecords(list []*entity.SQLDiffRecord) {
	m := make(map[string]*entity.SQLDiffRecord)
	for _, item := range list {
		_, ok := m[item.UnicKey]
		if ok {
			s.SLog.Warning("unic key is not unq").Set("key", item.UnicKey).Print()
			continue
		}
		m[item.UnicKey] = item
	}
}

func (s *UploadService) SendCardToAllUser(repoName string, commitURL string) error {
	userList := conf.Cardsend.SqldiffReceiver
	for _, user := range userList {
		if err := s.sendCard(repoName, commitURL, user); err != nil {
			s.Context.SLog.Error("send card to user error").Set("user_id", user).Set("commit url", commitURL).SetErr(err).Print()
		} else {
			s.Context.SLog.Info("successfully sent card to user").Set("user_id", user).Set("commit url", commitURL).Print()
		}
	}
	return nil
}

func (s *UploadService) sendCard(repoName string, commitURL string, sendUser string) error {
	client := remote.NewCardSendClient(s.Context)

	title := fmt.Sprintf("【SQL DIFF】您有新的sql变更需要review ")

	url := fmt.Sprintf("http://%s:%d/sqldata?method=diffview&commit_url=%s&send_user=%s", conf.Cardsend.SelfHost, conf.Cardsend.SelfPort, commitURL, sendUser)
	body := &remote.CardSendPost{
		UserData: &remote.CardSendPostUserData{
			Title:		title,
			ServiceName:	repoName,
			Desc:		title,
			Subtitle:	title,
			URL: &remote.CardSendPostUserDataURL{
				URL: &remote.CardSendPostUserDataURLURL{
					Type:	"newUrl",
					Contents: &remote.CardSendPostUserDataURLURLContents{
						Title:	"点击前往",
						Mobile: &remote.CardSendPostUserDataURLURLContentsMobile{
							OpenType:	"app_container",
							URL:		url,
						},
						Pc: &remote.CardSendPostUserDataURLURLContentsPc{
							OpenType:	"normal",
							URL:		url,
						},
					},
				},
			},
		},
		ReceiverID:	sendUser,
		TemplateID:	"b84802d37a624255a044267fba988fd7",
		ReceiverType:	"user",
		UserMsg:	false,
		AppName:	"easy_helper",
	}

	header := &remote.CardSendHeader{
		ContentType: "application/json",
	}
	err := client.Do(body, header)
	if err != nil {
		s.Context.SLog.Error("send card to user error").Set("user_id", sendUser).SetErr(err).Print()
		return err
	}
	s.Context.SLog.Info("successfully sent card to user").Set("user_id", sendUser).Print()

	return nil
}
