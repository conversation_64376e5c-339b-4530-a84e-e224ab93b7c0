module icode.baidu.com/baidu/netdisk/easy-coderule-data-server

go 1.21

require (
	github.com/coocood/freecache v1.2.5-0.20240412151032-eadf666f037f
	github.com/mitchellh/mapstructure v1.5.0
	github.com/sergi/go-diff v1.4.0
	github.com/stretchr/testify v1.8.2
	icode.baidu.com/baidu/netdisk/easy-go-sdk v0.0.0-20250715023816-f25e00eade39
	icode.baidu.com/baidu/netdisk/tangram-iface v0.0.0-20250627074309-31f7046591e1
)

require (
	github.com/cespare/xxhash/v2 v2.1.2 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/garyburd/redigo v1.6.4 // indirect
	github.com/go-playground/locales v0.14.0 // indirect
	github.com/go-playground/universal-translator v0.18.0 // indirect
	github.com/go-playground/validator/v10 v10.11.0 // indirect
	github.com/jinzhu/copier v0.3.5 // indirect
	github.com/leodido/go-urn v1.2.1 // indirect
	github.com/oleiade/reflections v1.0.1 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	golang.org/x/crypto v0.13.0 // indirect
	golang.org/x/sys v0.12.0 // indirect
	golang.org/x/text v0.13.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	icode.baidu.com/baidu/netdisk/easy-adapter v0.0.0-20250626031733-eded5c5b5fa0 // indirect
	icode.baidu.com/baidu/netdisk/easy-common v0.0.0-20231229091946-d81c3e61ef3b // indirect
	icode.baidu.com/baidu/netdisk/tangram v0.0.0-20250526025708-9403c4f00879 // indirect
	icode.baidu.com/baidu/netdisk/tangram-mock v0.0.0-20250526025826-c2b579a7f4b9 // indirect
	icode.baidu.com/baidu/netdisk/tangram-replay-mock v0.0.0-20230904121732-e85f3a9b8672 // indirect
	icode.baidu.com/baidu/netdisk/tangram-third-party v0.0.0-20250526021450-2af171f6bac6 // indirect
)
